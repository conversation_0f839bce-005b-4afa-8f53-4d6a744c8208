# -*- coding: utf-8 -*-

"""
Этот скрипт предназначен для асинхронной загрузки PDF-квитанций из API системы электронного документооборота M.E.Doc.
Он получает протокол документа (включая все квитанции) в виде единого PDF-файла по глобальному идентификатору документа.

Основные функции:
- download_receipt_protocol_pdf: Загружает PDF-протокол/квитанцию для указанного документа.
- fetch_one_url: Выполняет асинхронные GET-запросы с обработкой ошибок.

Требования:
- Установленные переменные окружения (PG_HOST_LOCAL).
- Библиотеки: aiohttp, python-dotenv.

Использование:
- Укажите doc_id документа и output_dir для сохранения.
- Запустите скрипт для загрузки квитанции.
"""

import asyncio
import base64
import os
import logging
from typing import Optional, Dict, Any
from dotenv import load_dotenv
import aiohttp

# --- Настройка (такая же, как в вашем основном скрипте) ---
load_dotenv()
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
HOSTNAME_PUBLIC = os.getenv("PG_HOST_LOCAL", "*************")
ID_ORG = 781
SEMAPHORE = asyncio.Semaphore(5)

# --- Существующая функция для выполнения запросов ---
async def fetch_one_url(session: aiohttp.ClientSession, url: str, semaphore: asyncio.Semaphore) -> Optional[Any]:
    """Выполняет один GET-запрос и возвращает JSON-ответ."""
    async with semaphore:
        try:
            headers = {"Connection": "close"}
            timeout = aiohttp.ClientTimeout(total=300)
            async with session.get(url, headers=headers, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.error(f"Ошибка запроса к {url}. Статус: {response.status}, Ответ: {await response.text()}")
                    return None
        except asyncio.TimeoutError:
            logging.error(f"Таймаут при запросе к {url}. Сервер не ответил вовремя.")
            return None
        except Exception as e:
            logging.error(f"Непредвиденная ошибка при запросе к {url}: {e}")
            return None

# --- НОВАЯ ФУНКЦИЯ ДЛЯ ЗАГРУЗКИ КВИТАНЦИИ В PDF ---
async def download_receipt_protocol_pdf(
    session: aiohttp.ClientSession,
    doc_id: str,
    output_dir: str,
    semaphore: asyncio.Semaphore
) -> Optional[str]:
    """
    Загружает протокол документа (включая все квитанции) в виде единого PDF-файла.

    Args:
        session (aiohttp.ClientSession): Активная сессия aiohttp.
        doc_id (str): Глобальный идентификатор основного документа (например, накладной).
        output_dir (str): Папка для сохранения PDF-файла квитанции.
        semaphore (asyncio.Semaphore): Семафор для ограничения запросов.

    Returns:
        Optional[str]: Путь к сохраненному файлу или None в случае ошибки.
    """
    logging.info(f"Запрос PDF-протокола для документа с ID: {doc_id}")
    
    # Формируем URL к эндпоинту GetDocProtocol
    # url = f"http://{HOSTNAME_PUBLIC}:63777/api/Info/GetDocProtocol?idOrg=781&exDocID={doc_id}"
    url = f"http://{HOSTNAME_PUBLIC}:63777/api/Info/GetDocKVT?idOrg=781&exDocID={doc_id}"
    
    # Выполняем запрос
    data = await fetch_one_url(session, url, semaphore)
    
    if not data:
        logging.error(f"Не удалось получить данные протокола для doc_id: {doc_id}.")
        return None

    try:
        # Ответ обычно представляет собой список с одним элементом
        if not data or not isinstance(data, dict):
            logging.error(f"Неожиданный формат ответа от GetDocProtocol для doc_id: {doc_id}")
            return None
        
        protocol_info = data
        file_raw = protocol_info.get('File')
        file_name_from_api = protocol_info.get('FileName') # Обычно "Протокол.pdf"

        if not file_raw or not file_name_from_api:
            logging.error(f"В ответе GetDocProtocol отсутствуют 'File' или 'FileName' для doc_id: {doc_id}")
            return None
        
        # Создаем уникальное и информативное имя файла
        # Например: 0D9CD016-6CDA-427A-9D19-824667F2BE12_Протокол.pdf
        final_file_name = f"{file_name_from_api}"
        
        # Создаем папку, если ее нет
        os.makedirs(output_dir, exist_ok=True)
        file_path = os.path.join(output_dir, f"{final_file_name} kvt.pdf")
        
        # Декодируем Base64 и сохраняем файл
        file_data = base64.b64decode(file_raw)
        with open(file_path, 'wb') as f:
            f.write(file_data)
            
        logging.info(f"✅ Протокол/квитанция успешно сохранена: {file_path}")
        return file_path

    except Exception as e:
        logging.error(f"Произошла непредвиденная ошибка при обработке протокола для doc_id {doc_id}: {e}")
        return None


async def main():
    """Основная функция для демонстрации загрузки квитанции."""
    
    # --- ЗАМЕНИТЕ ЭТИ ЗНАЧЕНИЯ НА РЕАЛЬНЫЕ ---
    # ID документа, для которого вы хотите скачать квитанцию
    target_doc_id = "1804afda-7b37-11ef-81c1-001dd8b740bc" 
    # Папка, куда будет сохранен PDF-файл квитанции
    output_folder = "receipts"
    
    logging.info("--- Запуск демонстрации загрузки PDF-квитанции ---")
    
    async with aiohttp.ClientSession() as session:
        saved_path = await download_receipt_protocol_pdf(
            session=session,
            doc_id=target_doc_id,
            output_dir=output_folder,
            semaphore=SEMAPHORE
        )
        
        if saved_path:
            print(f"\nИтог: PDF-файл квитанции успешно сохранен по пути: {saved_path}")
        else:
            print(f"\nИтог: Не удалось скачать квитанцию для документа с ID: {target_doc_id}")


if __name__ == '__main__':
    asyncio.run(main())
