# http://{hostname_public}:63777/Swagger/index.html
import asyncio

import pandas as pd

from AsyncHTTP import fetch_all_urls
from AsyncPostgresql import clear_table, df_to_sql
import os

hostname_public = os.getenv('MEDOC_API_HOST')
GET_DOC_INFO_URL = f"http://{hostname_public}:63777/api/Info/GetDocInfo?docID="
TAB_NAME = "medoc_doc_info"


# Отримати повну інформацію по документу
async def get_url_doc_info_list(df: pd.DataFrame):
    if 'doc_id' in df.columns:
        df['doc_id'] = df['doc_id'].astype(str)
        df['url'] = GET_DOC_INFO_URL + df['doc_id']

    return df


async def save_doc_info(df_source: pd.DataFrame):
    try:
        df = await get_url_doc_info_list(df_source)
        if df.empty:
            return None
        urls = list(df['url'])
        doc_info = await fetch_all_urls(urls)
        if (not doc_info and not isinstance(doc_info, dict) and not isinstance(doc_info, list)
                and not doc_info[0] and not isinstance(doc_info[0][0], dict)) :
            return None
        col = {key.lower(): value for key, value in doc_info[0][0].items()}.keys()
        source = [list(i2.values()) for i1 in doc_info for i2 in i1]
        df_source = pd.DataFrame(source, columns=col)
        # await clear_table(TAB_NAME)
        await df_to_sql(df_source, TAB_NAME)
    except Exception as e:
        print(f"ERROR: save_doc_info: {e}")


def save_doc_info_sync(df_source: pd.DataFrame):
    """Синхронная версия функции для сохранения информации о документах"""
    return asyncio.run(save_doc_info(df_source))


if __name__ == '__main__':
    df = pd.DataFrame()
    asyncio.run(save_doc_info(df))
