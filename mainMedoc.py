# mainMedoc.py - Оптимизированная версия с сохранением оригинальной логики

import asyncio
import os
from datetime import date, datetime
from dateutil.relativedelta import relativedelta
from dateutil import parser
import xmltodict
import logging
from typing import Optional
import aiohttp

from AsyncHTTP import fetch_one_url, check_server_availability, wait_for_server_availability
from DeleteDoubleRowsFromTable import delete_double_rows_from_table
from GoogleSheetsPostgresql import google_sheets_update_register
from MedocConfig import doc_type_name
from SaveDocToPdf import create_table, main_medoc_save_doc_to_pdf
from Views.vwReestrAndDocInfo import vw_reestr_and_docinfo_async
from tGetDocInfo import save_doc_info
from tRegister import save_register
from dotenv import load_dotenv
load_dotenv()

hostname_public = os.getenv('MEDOC_API_HOST')

# Настройка логирования
logger = logging.getLogger(__name__)

doc_type = doc_type_name['Всі']  # 10100

# Конфигурация повторных попыток
MAX_RETRIES = 3
RETRY_DELAY = 5
SEMAPHORE_LIMIT = 5  # Ограничение одновременных операций


# Отримання квитанцій документа
async def get_url_checks(cardcode):
    return f"http://{hostname_public}:63777/api/Info/GetDocKVT?cardCode={cardcode}"


# Пошук і отримання ідентифікатора установи за заданими ЄДРПОУ установи та номером філії
async def get_idorg(my_okpo) -> Optional[int]:
    """Получение ID организации с повторными попытками."""
    url = f"http://{hostname_public}:63777/api/System/GetOrgCode?edrpou={my_okpo}"

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            response = await fetch_one_url(url)
            return response
        except Exception as e:
            logger.error(f"Error getting idorg (attempt {attempt}): {e}")
            if attempt < MAX_RETRIES:
                await asyncio.sleep(RETRY_DELAY * attempt)
            else:
                return None


async def get_counterparty_name(xml_data):
    """Извлечение имени контрагента из XML с обработкой ошибок."""
    result = None
    try:
        if xml_data:
            xml_dict = xmltodict.parse(xml_data)
            result = xml_dict['DECLAR']['DECLARBODY']['HNAMEBUY']
    except Exception as e:
        logger.error(f"Error parsing counterparty name from XML: {e}")
    finally:
        return result


def get_doc_status(xml_data):
    """Извлечение статуса документа из XML с обработкой ошибок."""
    result = None
    try:
        if xml_data:
            xml_dict = xmltodict.parse(xml_data)
            result = xml_dict['DECLAR']['DECLARBODY']['HRESULT']
    except Exception as e:
        logger.error(f"Error parsing doc status from XML: {e}")
    finally:
        return result


async def get_url(**kwargs):
    idorg = kwargs['idorg']
    datefrom = kwargs['datefrom']
    dateend = kwargs['dateend']
    return (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg={idorg}"
            f"&docType={doc_type}&moveType=0&dateFrom={datefrom}&dateEnd={dateend}")


def get_date_from(control_date):
    """Преобразование даты с улучшенной обработкой ошибок."""
    try:
        if isinstance(control_date, datetime):
            return control_date.date()
        elif isinstance(control_date, date):
            return control_date
        else:
            return parser.parse(control_date).date()
    except Exception as e:
        logger.error(f"Error parsing date {control_date}: {e}")
        # Возвращаем текущую дату минус месяц как fallback
        return datetime.today().date() - relativedelta(months=1)


async def process_date_range(date_first: date, date_last: date) -> bool:
    """Обработка одного диапазона дат с повторными попытками."""
    date_one = date_first.strftime("%Y/%m/%d")
    date_two = date_last.strftime("%Y/%m/%d")

    logger.info(f"Processing date range: {date_one} - {date_two}")

    # Дополнительная проверка доступности перед критичной операцией
    if not await check_server_availability():
        logger.warning(f"Сервер недоступен для периода {date_one} - {date_two}")
        return False

    url = (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg=781"
           f"&docType={doc_type}&moveType=0&dateFrom={date_one}&dateEnd={date_two}")

    try:
        df = await save_register(url)
        if not df.empty:  # может быть None, если нет данных в периоде
            await save_doc_info(df)
            return True
        else:
            logger.info(f"No data for period {date_one} - {date_two}")
            return False
    except Exception as e:
        logger.error(f"Error processing date range {date_one} - {date_two}: {e}")
        return False


async def main_medoc(date_first: datetime.date):
    """Основная функция с улучшенной обработкой ошибок."""
    try:
        # Проверяем доступность сервера в самом начале
        logger.info(f"Проверка доступности сервера {hostname_public}:63777...")
        if not await check_server_availability():
            logger.warning(f"Сервер {hostname_public}:63777 недоступен. Ожидание...")
            # Ждем доступности сервера до 5 минут (10 попыток по 30 секунд)
            if not await wait_for_server_availability(max_attempts=10, delay=30):
                logger.critical(f"КРИТИЧЕСКАЯ ОШИБКА: Сервер {hostname_public}:63777 недоступен после всех попыток!")
                logger.critical("Проверьте:")
                logger.critical("1. Запущен ли сервер Medoc")
                logger.critical("2. Правильный ли адрес в переменной окружения PG_HOST")
                logger.critical("3. Доступен ли порт 63777")
                logger.critical("4. Нет ли блокировки файрволом")
                raise Exception("Сервер недоступен")

        logger.info(f"Сервер {hostname_public}:63777 доступен")

        # Создаем таблицы
        await create_table()

        # Опционально очищаем таблицы (закомментировано в оригинале)
        # await clear_table('medoc_reestr')
        # await clear_table('medoc_doc_info')

        date_first = get_date_from(date_first)
        current_date = date_first

        # Счетчики для статистики
        total_ranges = 0
        successful_ranges = 0

        # ВАЖНО: Сохраняем оригинальную логику последовательной обработки по неделям
        # Если нужна параллельная обработка, можно использовать семафор:
        # semaphore = asyncio.Semaphore(SEMAPHORE_LIMIT)

        while current_date <= datetime.today().date():
            # Добавляем неделю к текущей дате
            date_last = current_date + relativedelta(weeks=1)

            total_ranges += 1

            # Обрабатываем диапазон с повторными попытками
            success = False
            for attempt in range(1, MAX_RETRIES + 1):
                try:
                    success = await process_date_range(current_date, date_last)
                    if success:
                        successful_ranges += 1
                        break
                except Exception as e:
                    logger.error(f"Error processing range (attempt {attempt}): {e}")
                    if attempt < MAX_RETRIES:
                        await asyncio.sleep(RETRY_DELAY * attempt)

            if not success:
                logger.warning(f"Failed to process range {current_date} - {date_last} after all attempts")

            # Переходим к следующему периоду
            current_date = date_last + relativedelta(days=1)

        logger.info(f"Date ranges processed: {successful_ranges}/{total_ranges} successful")

        # Создаем представление
        await vw_reestr_and_docinfo_async()

        # Удаляем дубликаты
        delete_double_rows_from_table('medoc_reestr')
        delete_double_rows_from_table('medoc_doc_info')

        # Отправляем менеджерам на почту счета в PDF
        await main_medoc_save_doc_to_pdf()

        # Обновляем таблицу в Google Sheets (закладка Medoc)
        google_sheets_update_register()

        logger.info("Medoc processing completed")

    except Exception as e:
        logger.critical(f"Critical error in main_medoc: {e}")
        raise


if __name__ == '__main__':
    # Настройка логирования
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('medoc_main.log'),
            logging.StreamHandler()
        ]
    )

    start = datetime.now()
    print(f"Start: {start}")

    date_first = datetime.today().date() - relativedelta(months=1)

    try:
        asyncio.run(main_medoc(date_first))
    except Exception as e:
        logger.critical(f"Failed to run main_medoc: {e}")

    end = datetime.now()
    print(f"End: {end}, Duration: {end - start}")