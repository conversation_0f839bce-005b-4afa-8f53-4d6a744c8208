# -*- coding: utf-8 -*-

"""
Этот скрипт предназначен для извлечения текста из PDF-файлов с помощью Apache Tika.
Apache Tika - это набор инструментов для обнаружения и извлечения метаданных и текста из различных типов файлов.

Основные функции:
- extract_text_from_pdf(pdf_path): Извлекает текст из PDF-файла с помощью Tika.

Требования:
- Установленная Java JDK (указать путь в JAVA_HOME).
- Установленная библиотека tika-python (pip install tika).

Использование:
- Укажите путь к PDF-файлу в переменной pdf_path.
- Запустите скрипт для извлечения и вывода текста.
"""

import os
from tika import parser

# Укажите путь к Java в переменной окружения PATH
os.environ['JAVA_HOME'] = r'c:\Program Files\Java\jdk-17'
os.environ['PATH'] = os.environ['JAVA_HOME'] + r'\bin;' + os.environ['PATH']


def extract_text_from_pdf(pdf_path):
    # Parse the PDF file
    raw = parser.from_file(pdf_path)
    # Extract the content
    text = raw['content']
    return text


if __name__ == '__main__':
    pdf_path = r'C:\Rasim\Python\TaxGovUa\32490244\202409\Видаткова накладна\Видаткова накладна №10448 від 05 09 2024 NoSign.pdf'
    extracted_text = extract_text_from_pdf(pdf_path)
    print(extracted_text)
