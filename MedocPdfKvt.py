# -*- coding: utf-8 -*-

"""
Этот скрипт предназначен для асинхронной загрузки всех PDF-квитанций для указанного документа из API системы M.E.Doc.
Он получает список квитанций по ID документа, затем скачивает каждую квитанцию в формате PDF.

Основные функции:
- download_receipts_for_document: Находит и скачивает все квитанции для документа.
- fetch_one_url: Выполняет асинхронные GET-запросы с обработкой ошибок.

Требования:
- Установленные переменные окружения (PG_HOST_LOCAL).
- Библиотеки: aiohttp, python-dotenv.

Использование:
- Укажите main_doc_id документа и output_dir для сохранения.
- Запустите скрипт для загрузки всех квитанций.
"""

import asyncio
import base64
import os
import logging
from typing import Optional, List, Dict, Any
from dotenv import load_dotenv
import aiohttp

# --- Настройка ---
# Загружаем переменные окружения (например, IP-адрес сервера) из файла .env
load_dotenv()

# Настраиваем логирование для вывода информации о процессе
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Глобальная конфигурация ---
# IP-адрес или имя хоста сервера M.E.Doc.
HOSTNAME_PUBLIC = os.getenv("PG_HOST_LOCAL", "*************")
# ID вашей организации в M.E.Doc.
ID_ORG = 781
# Ограничиваем количество одновременных сетевых запросов
SEMAPHORE = asyncio.Semaphore(5)

# --- Основные функции ---

async def fetch_one_url(session: aiohttp.ClientSession, url: str, semaphore: asyncio.Semaphore) -> Optional[Any]:
    """
    Выполняет один GET-запрос, обрабатывает ошибки и возвращает JSON-ответ.
    """
    # Ожидаем свободный "слот" от семафора
    async with semaphore:
        try:
            # Заголовок для стабильности соединения
            headers = {"Connection": "close"}
            # Устанавливаем общий таймаут на 5 минут для долгих запросов
            timeout = aiohttp.ClientTimeout(total=300)
            
            async with session.get(url, headers=headers, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.error(f"Ошибка запроса к {url}. Статус: {response.status}, Ответ: {await response.text()}")
                    return None
        except asyncio.TimeoutError:
            logging.error(f"Таймаут при запросе к {url}. Сервер не ответил вовремя.")
            return None
        except Exception as e:
            logging.error(f"Непредвиденная ошибка при запросе к {url}: {e}")
            return None

async def download_receipts_for_document(
    session: aiohttp.ClientSession,
    main_doc_id: str,
    output_dir: str,
    semaphore: asyncio.Semaphore
) -> List[str]:
    """
    Находит и скачивает все квитанции для указанного документа в формате PDF.

    Args:
        session (aiohttp.ClientSession): Активная сессия aiohttp.
        main_doc_id (str): ID основного документа (налоговой накладной).
        output_dir (str): Папка для сохранения PDF-файлов.
        semaphore (asyncio.Semaphore): Семафор для ограничения запросов.

    Returns:
        List[str]: Список путей к успешно сохраненным файлам.
    """
    logging.info(f"--- Начало процесса загрузки квитанций для документа {main_doc_id} ---")
    
    # --- Шаг 1: Получаем список всех квитанций и их cardcode ---
    logging.info(f"Шаг 1: Запрос списка квитанций (GetDocKVT)...")
    url_get_kvt = f"http://{HOSTNAME_PUBLIC}:63777/api/Info/GetDocKVT?idOrg={ID_ORG}&docID={main_doc_id}"
    
    receipts_list = await fetch_one_url(session, url_get_kvt, semaphore)
    
    if not receipts_list or not isinstance(receipts_list, list):
        logging.error(f"Не удалось получить список квитанций или ответ имеет неверный формат для doc_id: {main_doc_id}")
        return []

    logging.info(f"Найдено {len(receipts_list)} квитанций для документа.")
    
    saved_files = []
    
    # --- Шаг 2: Для каждой квитанции запрашиваем ее PDF ---
    for i, receipt_data in enumerate(receipts_list):
        receipt_number = i + 1
        receipt_card_code = receipt_data.get("cardcode")

        if not receipt_card_code:
            logging.warning(f"У квитанции №{receipt_number} отсутствует 'cardcode', пропускаем.")
            continue
            
        logging.info(f"Шаг 2.{receipt_number}: Запрос PDF для квитанции с cardcode={receipt_card_code}...")
        url_print_pdf = f"http://{HOSTNAME_PUBLIC}:63777/api/Info/PrintDocPDF?idOrg={ID_ORG}&cardCode={receipt_card_code}"
        
        pdf_data = await fetch_one_url(session, url_print_pdf, semaphore)

        if not pdf_data or not isinstance(pdf_data, list) or not pdf_data[0]:
            logging.error(f"Не удалось получить PDF для cardcode квитанции: {receipt_card_code}")
            continue

        try:
            pdf_info = pdf_data[0]
            file_raw_base64 = pdf_info.get('File')
            file_name_from_api = pdf_info.get('FileName', f"Квитанция_{receipt_card_code}.pdf")

            if not file_raw_base64:
                logging.error(f"В ответе PrintDocPDF отсутствует поле 'File' для cardcode: {receipt_card_code}")
                continue
                
            # Создаем уникальное имя файла, связывая его с основным документом
            final_file_name = f"{main_doc_id}_{file_name_from_api}"
            
            os.makedirs(output_dir, exist_ok=True)
            file_path = os.path.join(output_dir, final_file_name)
            
            file_data_bytes = base64.b64decode(file_raw_base64)
            with open(file_path, 'wb') as f:
                f.write(file_data_bytes)
                
            logging.info(f"✅ PDF квитанции №{receipt_number} успешно сохранен: {file_path}")
            saved_files.append(file_path)

        except Exception as e:
            logging.error(f"Произошла непредвиденная ошибка при обработке PDF квитанции {receipt_card_code}: {e}")

    return saved_files


async def main():
    """Основная функция для демонстрации."""
    
    # --- ВВЕДИТЕ ВАШИ ДАННЫЕ ЗДЕСЬ ---
    # ID основного документа (налоговой накладной), для которого вы хотите скачать квитанции
    TARGET_DOC_ID = "1804afda-7b37-11ef-81c1-001dd8b740bc" 
    # Папка, куда будут сохранены PDF-файлы квитанций
    OUTPUT_FOLDER = "receipts"
    
    if TARGET_DOC_ID == "ВАШ_DOC_ID_ЗДЕСЬ":
        print("!!! ПОЖАЛУЙСТА, ОТРЕДАКТИРУЙТЕ СКРИПТ И УКАЖИТЕ РЕАЛЬНЫЙ DOC_ID В ПЕРЕМЕННОЙ TARGET_DOC_ID !!!")
        return

    logging.info("--- Запуск загрузки PDF-квитанций ---")
    
    async with aiohttp.ClientSession() as session:
        saved_paths = await download_receipts_for_document(
            session=session,
            main_doc_id=TARGET_DOC_ID,
            output_dir=OUTPUT_FOLDER,
            semaphore=SEMAPHORE
        )
        
        print("\n" + "="*40)
        print("--- ИТОГИ ЗАГРУЗКИ КВИТАНЦИЙ ---")
        if saved_paths:
            print(f"✅ Успешно скачано {len(saved_paths)} квитанций в папку '{OUTPUT_FOLDER}'.")
        else:
            print(f"❌ Не удалось скачать квитанции для документа с ID: {TARGET_DOC_ID}")
        print("="*40)


if __name__ == '__main__':
    asyncio.run(main())
