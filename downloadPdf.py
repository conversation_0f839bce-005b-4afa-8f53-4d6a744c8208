# -*- coding: utf-8 -*-
# Выгрузка из Medoc документов клиента в PDF за указанный период
# downloadPdf.py
# https://aistudio.google.com/prompts/11kMlSDfBYrSB_wHXkZIaaRxGxr1cHQzm

import asyncio
import base64
import json
import os
import re
import logging
import shutil
from datetime import datetime, date
from typing import Optional, List, Dict, Any, Tuple
from collections import defaultdict
from dotenv import load_dotenv
import aiohttp
from dateutil.relativedelta import relativedelta
from dateutil.parser import parse
import fitz  # PyMuPDF
import openpyxl # <-- ДОБАВЛЕНО: для работы с Excel

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# --- Конфигурация ---
HOSTNAME_PUBLIC = os.getenv("PG_HOST_LOCAL", "*************")
ID_ORG = 781


def clean_filename(filename: str) -> str:
    """Удаляет недопустимые символы из имени файла и лишние пробелы."""
    return re.sub(r'[\\/*?:"<>|]', "", filename).strip()


def doc_type_short(doc_type: str) -> str:
    doc_type = doc_type.lower()
    if doc_type == 'податкова накладна':
        return 'ПН'
    if doc_type in ['дoдаток','додаток']:  # выглядят одинаково, но разные буквы о
        return 'РК'    
    
    return doc_type.upper()


def get_doc_type_name(docname: Optional[str]) -> str:
    """
    Извлекает из имени документа описательную часть до знака '№'.
    Если знак '№' в строке отсутствует, возвращает исходную строку.
    Возвращает '_Інше' для пустого ввода.
    """
    if not docname:
        return "_Інше"
    separator_index = docname.find('№')
    if separator_index == -1:
        return doc_type_short(docname.strip())
    return doc_type_short(docname[:separator_index].strip())


def split_date_range_by_month(start_date_str: str, end_date_str: str) -> List[Tuple[date, date]]:
    date_format = '%Y/%m/%d'
    start_dt = datetime.strptime(start_date_str, date_format).date()
    end_dt = datetime.strptime(end_date_str, date_format).date()
    if start_dt + relativedelta(months=1) > end_dt:
        return [(start_dt, end_dt)]
    print("Диапазон дат слишком большой. Разбиваю на месячные интервалы...")
    date_ranges = []
    current_start = start_dt
    while current_start <= end_dt:
        chunk_end = current_start + relativedelta(months=1) - relativedelta(days=1)
        if chunk_end > end_dt:
            chunk_end = end_dt
        date_ranges.append((current_start, chunk_end))
        current_start = chunk_end + relativedelta(days=1)
    return date_ranges


async def fetch_one_url(session: aiohttp.ClientSession, url: str, semaphore: asyncio.Semaphore) -> Optional[Any]:
    async with semaphore:
        try:
            headers = {"Connection": "close"}
            timeout = aiohttp.ClientTimeout(total=60)
            async with session.get(url, headers=headers, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.error(f"Ошибка запроса к {url}. Статус: {response.status}, Ответ: {await response.text()}")
                    return None
        except Exception as e:
            logging.error(f"Непредвиденная ошибка при запросе к {url}: {e}")
            return None


def is_pdf_defective(file_path: str, letter_ratio_threshold: float = 0.4) -> bool:
    """
    Проверяет PDF на дефектность, анализируя долю буквенных символов в тексте.
    """
    try:
        doc = fitz.open(file_path)
        full_text = ""
        for page in doc:
            full_text += page.get_text()
        doc.close()

        clean_text = "".join(full_text.split())
        if not clean_text:
            return False

        total_chars = len(clean_text)
        letter_chars = sum(1 for char in clean_text if char.isalpha())
        letter_ratio = letter_chars / total_chars

        logging.info(
            f"Анализ файла '{os.path.basename(file_path)}': "
            f"Доля букв: {letter_ratio:.2f} ({letter_chars} из {total_chars}). "
            f"Порог: {letter_ratio_threshold}"
        )
        return letter_ratio < letter_ratio_threshold
    except Exception as e:
        logging.error(f"Критическая ошибка при обработке PDF '{file_path}': {e}")
        return True


async def get_document_as_pdf(session: aiohttp.ClientSession, doc: Dict[str, Any], semaphore: asyncio.Semaphore,
                              facsimile: bool, suffix: str = "") -> Optional[Tuple[str, str]]: # <-- ИЗМЕНЕНО: возвращает кортеж
    doc_id = doc.get('doc_id')
    url = f"http://{HOSTNAME_PUBLIC}:63777/api/Info/PrintDocPDF?idOrg={ID_ORG}&docID={doc_id}&facsimile={str(facsimile).lower()}"
    data = await fetch_one_url(session, url, semaphore)
    if not data:
        logging.warning(f"Нет ответа от API для doc_id: {doc_id} (facsimile={facsimile}).")
        return None
    try:
        if not isinstance(data, list) or len(data) == 0:
            logging.error(f"Неожиданный или пустой формат ответа API для doc_id: {doc_id}")
            return None

        document_info = data[0]

        if not isinstance(document_info, dict):
            logging.error(f"Первый элемент не является словарем для doc_id: {doc_id}")
            return None

        file_raw = document_info.get('File')
        file_name_from_api = document_info.get('FileName')
        doc_type = get_doc_type_name(file_name_from_api)
        output_dir = clean_filename(doc['partner_edrpou'])

        if not file_raw:
            logging.error(f"В ответе API отсутствует 'File' для doc_id: {doc_id}")
            return None

        base_name = ""
        period = ''
        if file_name_from_api:
            doc_num = doc.get('doc_num', '')
            doc_date_str = doc.get('doc_date')
            formatted_date = ''
            if doc_date_str:
                try:
                    parsed_date = parse(doc_date_str, dayfirst=True)
                    formatted_date = parsed_date.strftime('%d %m %Y')
                    period = parsed_date.strftime('%Y%m')
                except (ValueError, TypeError):
                    pass
                base_name = f"{doc_type} {doc_num} {formatted_date}"

        if not base_name:
            logging.error(f"Не удалось сформировать базовое имя для doc_id: {doc_id}. Используется запасное имя.")
            base_name = f"UNNAMED_{doc_id}"

        final_name = base_name.replace('.', ' ').upper()
        final_file_name = f"{clean_filename(final_name)}{suffix}.pdf"

        good_save_dir = os.path.join(output_dir, period, doc_type)
        os.makedirs(good_save_dir, exist_ok=True)
        file_path = os.path.join(good_save_dir, final_file_name)

        file_data = base64.b64decode(file_raw)
        with open(file_path, 'wb') as f:
            f.write(file_data)

        if is_pdf_defective(file_path):
            bad_dir = os.path.join(output_dir, 'Bad')
            os.makedirs(bad_dir, exist_ok=True)
            new_pdf_path = os.path.join(bad_dir, final_file_name)
            json_path = new_pdf_path.replace('.pdf', '.json')
            try:
                shutil.move(file_path, new_pdf_path)
                logging.warning(f"Обнаружен дефектный PDF: '{final_file_name}'. Файл перемещен в '{bad_dir}'")
            except OSError as e:
                logging.error(f"Не удалось переместить файл {file_path} в {new_pdf_path}: {e}")
                json_path = file_path.replace('.pdf', '.json')
            with open(json_path, 'w', encoding='utf-8') as f_json:
                json.dump(data, f_json, ensure_ascii=False, indent=4)

        return file_path, doc_type # <-- ИЗМЕНЕНО: возвращаем путь и тип документа
    except Exception as e:
        logging.error(f"Произошла непредвиденная ошибка при обработке doc_id {doc_id}: {e}")
        return None


async def download_documents_for_partner(session: aiohttp.ClientSession, partner_edrpou: str, date_from: str,
                                         date_end: str, semaphore: asyncio.Semaphore):
    base_output_dir = partner_edrpou
    print(f"Запуск процесса загрузки для партнёра {partner_edrpou} с {date_from} по {date_end}")
    print(f"Файлы будут сохранены в базовую папку:./{base_output_dir}")

    date_ranges = split_date_range_by_month(date_from, date_end)
    all_documents = []
    for start_chunk, end_chunk in date_ranges:
        chunk_from_str = start_chunk.strftime('%Y/%m/%d')
        chunk_end_str = end_chunk.strftime('%Y/%m/%d')
        print(f"Запрос списка документов за период: {chunk_from_str} - {chunk_end_str}")
        url = (f"http://{HOSTNAME_PUBLIC}:63777/api/Info/GetPrimaryReestr?"
               f"idOrg={ID_ORG}&docType=-1&moveType=0&dateFrom={chunk_from_str}&dateEnd={chunk_end_str}")
        documents_chunk = await fetch_one_url(session, url, semaphore)
        if documents_chunk:
            all_documents.extend(documents_chunk)
        else:
            logging.warning(f"Не удалось получить документы за период {chunk_from_str} - {chunk_end_str}.")
        await asyncio.sleep(1)

    if not all_documents:
        logging.warning("Не удалось получить данные о документах ни за один из периодов.")
        return

    partner_docs = [doc for doc in all_documents if doc.get('partner_edrpou') == partner_edrpou]

    if not partner_docs:
        print(f"Для партнёра {partner_edrpou} не найдены документы в указанном диапазоне дат.")
        return

    grouped_by_id = defaultdict(list)
    for doc in partner_docs:
        if doc.get('doc_id'):
            grouped_by_id[doc['doc_id']].append(doc)

    unique_partner_docs = []
    for doc_id, doc_group in grouped_by_id.items():
        if len(doc_group) == 1:
            unique_partner_docs.append(doc_group[0])
        else:
            try:
                sorted_group = sorted(
                    doc_group,
                    key=lambda d: datetime.fromisoformat(d.get('moddate', '1970-01-01T00:00:00')),
                    reverse=True
                )
                unique_partner_docs.append(sorted_group[0])
            except (ValueError, TypeError) as e:
                logging.warning(
                    f"Ошибка при сортировке дубликатов для doc_id {doc_id} (Ошибка: {e}). Будет использован первый найденный.")
                unique_partner_docs.append(doc_group[0])

    print(f"Всего найдено в реестре (с дубликатами): {len(partner_docs)} документов.")
    print(f"Найдено уникальных документов (с учетом 'moddate'): {len(unique_partner_docs)}. Начинаю загрузку PDF...")

    tasks = []
    for doc in unique_partner_docs:
        task = get_document_as_pdf(session, doc, semaphore, facsimile=True)
        tasks.append(task)

    results = await asyncio.gather(*tasks)

    failed_docs = []
    successful_count = 0
    excel_data = [] # <-- ДОБАВЛЕНО: список для данных Excel

    for doc, result in zip(unique_partner_docs, results):
        # <-- ИЗМЕНЕНО: Обработка кортежа из результата
        if result:
            result_path, doc_type = result
            successful_count += 1
            # <-- ДОБАВЛЕНО: Сбор данных для Excel
            doc_date_str = doc.get('doc_date')
            formatted_doc_date = ''
            if doc_date_str:
                try:
                    # Форматируем дату в привычный для Excel вид
                    formatted_doc_date = parse(doc_date_str).strftime('%d.%m.%Y')
                except (ValueError, TypeError):
                    formatted_doc_date = doc_date_str # Оставляем как есть, если не удалось распарсить

            excel_data.append([
                doc_type,
                formatted_doc_date,
                doc.get('doc_num', '')
            ])
        else:
            failed_docs.append(doc)

    total_files_on_disk = 0
    if os.path.exists(base_output_dir):
        for root, dirs, files in os.walk(base_output_dir):
            total_files_on_disk += len(files)

    print("\n" + "=" * 40)
    print("--- ИТОГИ ЗАГРУЗКИ ---")
    print(f"Найдено в реестре (с дубликатами): {len(partner_docs)}")
    print(f"Найдено уникальных документов: {len(unique_partner_docs)}")
    print(f"✅ Успешно загружено по данным скрипта: {successful_count}")
    print(f"💽 Фактически файлов в папках: {total_files_on_disk}")

    if failed_docs:
        failed_ids = [d.get('doc_id', 'N/A') for d in failed_docs]
        print(f"❌ Не удалось загрузить: {len(failed_docs)} файлов")
        print(f"   ID незагруженных документов: {failed_ids}")
    print("=" * 40 + "\n")

    # --- ДОБАВЛЕН БЛОК СОХРАНЕНИЯ В EXCEL ---
    if excel_data:
        try:
            # Создаем имя файла: код клиента + штамп времени
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            excel_filename = f"{partner_edrpou}_{timestamp}.xlsx"
            # Сохраняем в папку с кодом клиента
            excel_filepath = os.path.join(base_output_dir, excel_filename)

            # Создаем новую книгу Excel
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Список документов"

            # Добавляем заголовок
            ws.append(['Тип документа', 'Дата', 'Номер'])

            # Добавляем данные
            for row_data in excel_data:
                ws.append(row_data)
            
            # Автоподбор ширины колонок для красоты
            for col in ws.columns:
                max_length = 0
                column = col[0].column_letter # Получаем букву колонки
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(cell.value)
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column].width = adjusted_width

            # Сохраняем файл
            wb.save(excel_filepath)
            logging.info(f"Данные о документах сохранены в Excel файл: {excel_filepath}")

        except Exception as e:
            logging.error(f"Не удалось сохранить Excel файл. Ошибка: {e}")
    # --- КОНЕЦ БЛОКА EXCEL ---

    print("🎉 Все задачи по загрузке завершены.")


async def main():
    partner = '31316718'
    date_from = '2024/01/01'
    # date_to = '2024/05/31'
    date_to = datetime.today().strftime('%Y/%m/%d')
    
    # НЕ ИЗМЕНЯТЬ! При параллельной обработке в pdf файлах появляются некорректные символы
    semaphore = asyncio.Semaphore(1)  # ВАЖНО, НЕ ИЗМЕНЯТЬ!!!

    async with aiohttp.ClientSession() as session:
        await download_documents_for_partner(
            session=session,
            partner_edrpou=partner,
            date_from=date_from,
            date_end=date_to,
            semaphore=semaphore
        )


if __name__ == '__main__':
    asyncio.run(main())