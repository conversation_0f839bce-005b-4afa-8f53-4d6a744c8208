import sys
from cx_Freeze import setup, Executable

# Dependencies are automatically detected, but it might need fine tuning.
build_options = {
    'packages': ['asyncio', 'aiohttp', 'asyncpg', 'tkinter', 'tkcalendar', 'dotenv', 'json', 'logging', 'queue', 'threading', 'datetime', 'typing', 'os'],
    'excludes': [],
    'include_files': ['.env'] if sys.platform.startswith('win') else []
}

base = 'Win32GUI' if sys.platform.startswith('win') else None

executables = [
    Executable('create_doc_in_medoc_gui.py', base=base, target_name='create_doc_in_medoc_gui.exe')
]

setup(
    name='CreateDocInMedocGUI',
    version='1.0',
    description='GUI for updating documents in M.E.Doc',
    options={'build_exe': build_options},
    executables=executables
)
