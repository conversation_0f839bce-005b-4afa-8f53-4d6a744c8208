# https://cabinet.tax.gov.ua/tax-invoices/written
# информация взятая из квитанций в формате pdf о дате и статусе блокировки

import asyncio
import os
import sys
from pathlib import Path

cur_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(Path(cur_dir).parent.__str__())
from AsyncPostgresql import async_save_pg

TABLE_NAME = "t_tax_cabinet_erpn_api_block"
SQL_CREATE_TABLE = f"""
-- DROP TABLE IF EXISTS {TABLE_NAME} CASCADE;
CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
	id uuid DEFAULT uuid_generate_v4() NOT NULL, -- id
	code int8 NOT NULL, -- id квитанции
	ijcode int8 NOT NULL,
	reg_date timestamp NULL, -- дата регистрации
	reg_number varchar(12) NULL, -- номер регистрации
	receipt_date timestamp NULL, -- квтДата
	receipt_number varchar(20) NULL, -- квтНомер
	receipt_status varchar(200) NULL, -- статус НН/РК
	kvt_number numeric(3) NOT NULL, -- квт
	filename varchar(30) NULL, -- наименование файла pdf
    CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (id),
    CONSTRAINT {TABLE_NAME}_unq UNIQUE (code, ijcode, kvt_number)
  );

    COMMENT ON TABLE {TABLE_NAME} IS 'решения, статусы блокировки';
    COMMENT ON COLUMN {TABLE_NAME}.id IS 'id';
    COMMENT ON COLUMN {TABLE_NAME}.code IS 'id квитанции';
    COMMENT ON COLUMN {TABLE_NAME}.ijcode IS '';
    COMMENT ON COLUMN {TABLE_NAME}.reg_date IS 'дата регистрации';
    COMMENT ON COLUMN {TABLE_NAME}.reg_number IS 'номер регистрации';
    COMMENT ON COLUMN {TABLE_NAME}.receipt_date IS 'квтДата';
    COMMENT ON COLUMN {TABLE_NAME}.receipt_number IS 'квтНомер';
    COMMENT ON COLUMN {TABLE_NAME}.receipt_status IS 'статус НН/РК';
    COMMENT ON COLUMN {TABLE_NAME}.kvt_number IS 'квт';
    COMMENT ON COLUMN {TABLE_NAME}.filename IS 'наименование файла pdf';
"""

SQL_INSERT_RECEIPT_BLOCK = f"""
  INSERT INTO {TABLE_NAME} (
    code, 
    ijcode,
    reg_date,
    reg_number, 
    receipt_date,
    receipt_number, 
    receipt_status, 
    kvt_number
    )
  VALUES ($1::int8, $2::int8, $3, $4::varchar(12), $5, $6, $7, $8)
  ON CONFLICT (code, ijcode, kvt_number)
  DO NOTHING
  ;
"""

SQL_SELECT_RECEIPT_BLOCK = f"""
SELECT *
FROM (
    WITH erpn AS (
        SELECT DISTINCT
            erpn.code,
            erpn.ijcode,
            erpn.docrnn AS reg_number,
            erpn.impdate AS reg_date,
            erpn.hsmcsttname AS receipt_status,
            999 AS kvt_number,  -- используем только для документов НН и РК
            erpn.hsmcstt
        FROM t_tax_cabinet_erpn_api AS erpn
        UNION ALL 
        SELECT DISTINCT
            erpn.code,
            erpn.ijcode,
            erpn.docrnn AS reg_number,
            erpn.impdate AS reg_date,
            erpn.hsmcsttname AS receipt_status,
            1 AS kvt_number,  -- используем только для квитанций №1
            erpn.hsmcstt
        FROM t_tax_cabinet_erpn_api AS erpn
        UNION ALL 
        SELECT DISTINCT
            erpn.code,
            erpn.ijcode,
            erpn.docrnn AS reg_number,
            erpn.impdate AS reg_date,
            erpn.hsmcsttname AS receipt_status,
            CASE 
                WHEN erpn.kvt2 = 1 THEN 2
                ELSE NULL 
            END AS kvt_number,
            erpn.hsmcstt
        FROM t_tax_cabinet_erpn_api AS erpn
        UNION ALL 
        SELECT DISTINCT
            erpn.code,
            erpn.ijcode,
            erpn.docrnn AS reg_number,
            erpn.impdate AS reg_date,
            erpn.hsmcsttname AS receipt_status,
            CASE 
                WHEN erpn.kvt3 = 1 THEN 3
                ELSE NULL 
            END AS kvt_number,
            erpn.hsmcstt
        FROM t_tax_cabinet_erpn_api AS erpn
        UNION ALL 
        SELECT DISTINCT
            erpn.code,
            erpn.ijcode,
            erpn.docrnn AS reg_number,
            erpn.impdate AS reg_date,
            erpn.hsmcsttname AS receipt_status,
            CASE 
                WHEN erpn.kvt4 = 1 THEN 4
                ELSE NULL 
            END AS kvt_number,
            erpn.hsmcstt
        FROM t_tax_cabinet_erpn_api AS erpn
    )
    SELECT DISTINCT 
        erpn.code,
        erpn.ijcode,
        erpn.reg_number,
        erpn.reg_date,
        erpn.receipt_status,
        erpn.kvt_number
    FROM erpn
    WHERE kvt_number IS NOT NULL 
        AND
        erpn.hsmcstt IN (
                1,  -- реєстрацiю зупинено
                2,  -- вiдмовлено за рiшенням Комiсiї
                13,  -- вiдмовлено за рiшенням Комiсiї. "Скаргу не задоволено"
                14, 15, 18
            )      
) AS t

"""


SQL_INSERT_ADD_FROM_ERPN_API = f"""
INSERT INTO t_tax_cabinet_erpn_api_block (
    code, 
    ijcode,
    reg_number, 
    reg_date,
    receipt_status, 
    kvt_number
    )
    {SQL_SELECT_RECEIPT_BLOCK}
ON CONFLICT (code, ijcode, kvt_number)
DO NOTHING
;
"""

SQL_UPDATE_FILENAME = f"""
UPDATE {TABLE_NAME} AS t
SET filename = $1
WHERE t.code = $2
    AND t.kvt_number = $3
"""


SQL_UPDATE_RECEIPT_DATA = f"""
UPDATE {TABLE_NAME} AS t
SET receipt_date = $1,
    receipt_number = $2
WHERE t.code = $3
    AND t.kvt_number = $4
"""



async def main_t_tax_cabinet_erpn_api_block_async():
    result = await async_save_pg(SQL_CREATE_TABLE)
    print(f"{result}, table {TABLE_NAME} created")


if __name__ == '__main__':
    asyncio.run(main_t_tax_cabinet_erpn_api_block_async())
