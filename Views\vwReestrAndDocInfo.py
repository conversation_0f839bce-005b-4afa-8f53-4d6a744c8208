import asyncio
import os
import sys
from pathlib import Path

cur_dir = os.path.dirname(os.path.abspath(__file__))
cur_dir = Path(cur_dir).parent.__str__()
sys.path.append(cur_dir)
from AsyncPostgresql import async_save_pg

VIEW_NAME = 'v_medoc_reestr_and_docinfo'
SQL_CREATE_VIEW = f"""
    DROP VIEW IF EXISTS {VIEW_NAME} CASCADE;
    CREATE OR REPLACE VIEW {VIEW_NAME} AS
    SELECT DISTINCT
        court_name,
        partner_edrpou,
        partner_name,
        doc_date, 
        t.doc_num, 
        t.reg_num,
        round(t.docsum::numeric, 2) as docsum,
        round(t.vatsum::numeric, 2) as vatsum,
        cardCode,
        doc_move,
        movetype,
        sender,
        sendstt,
        sendsttname,
        docname,
        doc_id,
        firm_edrpou,
        doc_type,
        CASE
            WHEN docname ilike '%Податкова накладна%' OR docname ilike '%Видаткова%' THEN 'Продажа'
            WHEN docname ilike '%Догов%' THEN 'Договор'
            WHEN docname ilike '%Угода%' THEN 'Соглашение'
            WHEN docname ilike '%Додаток%' THEN 'Возврат'
            WHEN docname ilike '%Універсальний%' THEN 'Универсальный'
            WHEN docname ilike '%Повідомлення%' THEN 'Уведомление'
            WHEN docname ilike '%Акт%' OR docname ilike '%Атк%' OR docname ilike '%akt%' THEN 'Акт'
            WHEN docname ilike '%взаємозалік%' THEN 'Взаимозачет'
            WHEN docname ilike '%Рахунок%' OR docname ilike '%Счет%' THEN 'Счет'
            ELSE Null
        END doc_type_name
    FROM (
        SELECT 
            reestr.vatsum,
            reestr.regdate,
            reestr.doc_id,
            reestr.exdoc_id,
            reestr.cardcode,
            reestr.doc_type,
            reestr.doc_move,
            reestr.doc_num,
            reestr.doc_vd,
            reestr.doc_dept,
            reestr.doc_date,
            reestr.firm_edrpou,
            reestr.firm_ipn,
            reestr.partner_edrpou,
            reestr.partner_dept,
            reestr.patrner_ipn,
            reestr.partner_name,
            reestr.osobfio,
            reestr.charcode,
            reestr.sender,
            reestr.reg_num,
            reestr.docsum,
            reestr.senduser,
            reestr.gettime,
            reestr.approvecode,
            reestr.expdate,
            reestr.notation,
            reestr.approve,
            info.edrpou,
            info.orgname,
            info.dept,
            info.idorg,
            info.pertype,
            info.perdate,
            info.formcode,
            info.charcode,
            info.status,
            info.statusname,
            info.sendstt,
            info.sendsttname,
            info.crtdate,
            info.um,
            info.crtuser,
            info.crtuname,
            info.moddate,
            info.moduser,
            info.moduname,
            info.impdate,
            info.impuser,
            info.impuname,
            info.regdate,
            info.consoled,
            info.docname,
            info.docid,
            info.idparent,
            info.exdocid,
            info.notes,
            info.notation,
            info.movetype,
            info.intrash,
            info.lastupdate,
            info.impfilename,
            info.kvtstt,
            info.expdate,
            info.lastupdate = max(info.lastupdate) OVER (PARTITION BY reestr.doc_id) AS last_date_updated,
            court.court_name
        FROM medoc_reestr AS reestr
            LEFT JOIN medoc_doc_info AS info
                ON reestr.doc_id = info.docid
            LEFT JOIN t_court as court
                ON reestr.doc_date = court.doc_date
                 AND reestr.doc_num = court.doc_number
        ) AS t
    WHERE t.last_date_updated
    ORDER BY t.doc_date DESC
    ;

COMMENT ON COLUMN {VIEW_NAME}.court_name IS 'наименование суда';
COMMENT ON COLUMN {VIEW_NAME}.doc_move IS 'направление документа\n-1–всі напрямки;\n1–вихідний;\n2–вхідний';
COMMENT ON COLUMN {VIEW_NAME}.doc_type IS 'тип документа\n-1–Всі;\n10100–Податкова накладна;\n10101–Додаток 2';
GRANT SELECT ON TABLE {VIEW_NAME} TO user_prestige;

"""


async def vw_reestr_and_docinfo_async():
    result = await async_save_pg(SQL_CREATE_VIEW)
    print(result)


def vw_reestr_and_docinfo_sync():
    """Синхронная версия функции для создания представления"""
    result = asyncio.run(vw_reestr_and_docinfo_async())
    return result


if __name__ == '__main__':
    asyncio.run(vw_reestr_and_docinfo_async())
