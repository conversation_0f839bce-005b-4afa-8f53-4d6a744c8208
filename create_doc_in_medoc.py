# -*- coding: utf-8 -*-

"""
================================================================================
НАЗНАЧЕНИЕ ФАЙЛА:
--------------------------------------------------------------------------------
Этот скрипт предназначен для ИНТЕЛЛЕКТУАЛЬНОГО ОБНОВЛЕНИЯ документа
"Повідомлення..." (J1312603) в M.E.Doc.

Логика работы:
1.  Табличная часть документа ПОЛНОСТЬЮ ЗАМЕНЯЕТСЯ свежими данными из
    базы PostgreSQL.
2.  Шапка документа обновляется УСЛОВНО: поля, которые уже были заполнены,
    остаются без изменений. Поля, которые были пусты, заполняются данными
    из документа-источника (на основе анализа F11).

================================================================================
"""

import asyncio
import os
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any
from dotenv import load_dotenv
import aiohttp
import asyncpg

# --- Настройка ---
load_dotenv()
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Конфигурация API M.E.Doc ---
HOSTNAME_PUBLIC = os.getenv("PG_HOST_LOCAL", "*************")
SEMAPHORE = asyncio.Semaphore(5)

# --- Конфигурация подключения к PostgreSQL ---
DB_CONFIG = {
    "user": os.getenv("PG_USER"),
    "password": os.getenv("PG_PASSWORD"),
    "host": os.getenv("PG_HOST_LOCAL", "localhost"),
    "port": os.getenv("PG_PORT", 5432),
    "database": os.getenv("PG_DBNAME")
}

# --- Основные функции ---

async def get_blocked_invoices_from_db(
    config: Dict,
    buyer_okpo: str,
    start_date: str,
    end_date: str
) -> List[Dict[str, Any]]:
    """
    Подключается к PostgreSQL и извлекает данные о заблокированных НН/РК.
    """
    conn = None
    sql_query = """
    SELECT 
        ROW_NUMBER() OVER (ORDER BY crtdate, nmr, code) AS row_no,
        crtdate, nmr, docrnn, pkpinn, hnamebuy,
        r4100g11::numeric AS r4100g11,
        ndssm::numeric AS ndssm
    FROM public.t_tax_cabinet_erpn_api
    WHERE 
        crtdate >= $1 AND crtdate < $2
        AND (hsmcstt IN (1, 2, 13, 14, 15, 18) OR hsmcsttname ILIKE ANY (ARRAY['%вiдмовлено%','%зупинено%']))
        AND ftype = 0 AND cptin = $3::numeric
    ORDER BY crtdate, nmr;
    """
    try:
        logging.info(f"Подключение к PostgreSQL (host: {config['host']})...")
        conn = await asyncpg.connect(**config)
        logging.info("Успешно подключено. Выполнение запроса...")
        start_date_obj = datetime.strptime(start_date, '%d.%m.%Y')
        end_date_obj = datetime.strptime(end_date, '%d.%m.%Y')
        records = await conn.fetch(sql_query, start_date_obj, end_date_obj, buyer_okpo)
        logging.info(f"Получено {len(records)} записей из базы данных.")
        return [dict(rec) for rec in records]
    except Exception as e:
        logging.error(f"Ошибка при работе с PostgreSQL: {e}")
        return []
    finally:
        if conn:
            await conn.close()
            logging.info("Соединение с PostgreSQL закрыто.")


async def get_document_values(session: aiohttp.ClientSession, card_code: int) -> Optional[List[Dict[str, Any]]]:
    """
    ЭТАП 1: Получает текущее полное содержимое документа из M.E.Doc.
    """
    url = f"http://{HOSTNAME_PUBLIC}:63777/api/Document/GetValues"
    params = {"cardCode": card_code}
    logging.info(f"Шаг 1: Получение текущих данных документа CardCode={card_code}...")
    try:
        async with session.get(url, params=params) as response:
            if response.status == 200:
                logging.info("Данные документа успешно получены.")
                return await response.json()
            else:
                logging.error(f"Не удалось получить данные документа. Статус: {response.status}, Ответ: {await response.text()}")
                return None
    except Exception as e:
        logging.error(f"Исключение при получении данных документа: {e}")
        return None


async def update_document_with_header_fill(
    session: aiohttp.ClientSession,
    card_code: int,
    current_doc_data: List[Dict[str, Any]],
    blocked_invoices: List[Dict[str, Any]]
) -> bool:
    """
    ЭТАП 2 и 3: Формирует и отправляет обновленный документ.
    """
    logging.info(f"Шаг 2: Формирование тела запроса для обновления CardCode={card_code}...")
    
    # --- Данные-источник из F11 для заполнения пустых полей ---
    source_header_data = {
        "FIRM_DPACD": "1305",
        "FIRM_DPANM": "ГОЛОВНЕ УПРАВЛІННЯ ДПС У ЛЬВІВСЬКІЙ ОБЛАСТІ, ЛЬВІВСЬКА ДЕРЖАВНА ПОДАТКОВА ІНСПЕКЦІЯ",
        "FIRM_EDRPOU": "41098985",
        "FIRM_NAME": "ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ \"ПРЕСТИЖ ПРОДУКТ.К\"",
        "RUK": "Валяєв Расім Сейранович",
        "RUKINN": "2634316155"
    }

    # --- Логика условного обновления шапки ---
    final_payload = []
    current_header_fields = {field['NAME']: field for field in current_doc_data if field.get('TAB') == 0}

    # Проходим по полям из источника и решаем, нужно ли их добавлять/обновлять
    for name, source_value in source_header_data.items():
        existing_field = current_header_fields.get(name)
        # Если поле в документе существует, но его значение пустое
        if existing_field and not str(existing_field.get("VALUE", "")).strip():
            logging.info(f"Поле '{name}' пустое. Заполняем значением: '{source_value}'")
            existing_field["VALUE"] = source_value
        # Если поля в документе вообще нет
        elif not existing_field:
            logging.info(f"Поле '{name}' отсутствует. Добавляем со значением: '{source_value}'")
            current_header_fields[name] = {"TAB": 0, "LINE": 0, "NAME": name, "VALUE": source_value}
    
    # Добавляем все (обновленные и старые) поля шапки в итоговый запрос
    final_payload.extend(current_header_fields.values())

    # --- Логика полного обновления табличной части ---
    logging.info(f"Добавляем {len(blocked_invoices)} строк из БД в табличную часть...")
    for invoice in blocked_invoices:
        line_num = invoice.get("row_no")
        sum_with_vat = invoice.get('r4100g11') or 0
        vat_sum = invoice.get('ndssm') or 0
        sum_without_vat = sum_with_vat - vat_sum
        nmr_value = invoice.get('nmr')
        nmr_as_string = str(int(nmr_value)) if nmr_value is not None else ""

        final_payload.extend([
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A1", "VALUE": str(line_num)},
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A2", "VALUE": invoice['crtdate'].strftime('%d.%m.%Y') if invoice['crtdate'] else ""},
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A31", "VALUE": nmr_as_string},
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A4", "VALUE": ""},
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A5", "VALUE": str(invoice.get('docrnn', ''))},
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A6", "VALUE": str(invoice.get('pkpinn', ''))},
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A7", "VALUE": invoice.get('hnamebuy', '')},
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A8", "VALUE": f"{sum_without_vat:.2f}"},
            {"TAB": 1, "LINE": line_num, "NAME": "TAB1_A9", "VALUE": f"{vat_sum:.2f}"},
        ])

    # --- Отправка запроса на обновление ---
    url = f"http://{HOSTNAME_PUBLIC}:63777/api/Document/SetValues"
    params = {"cardCode": card_code, "overwrite": True}
    
    logging.info(f"Шаг 3: Отправка POST-запроса на обновление CardCode={card_code}...")
    
    async with SEMAPHORE:
        try:
            async with session.post(url, params=params, json=final_payload) as response:
                if response.status == 200:
                    result = await response.json()
                    if result.get("Code") == 0:
                        logging.info(f"✅ УСПЕХ! Документ CardCode={card_code} успешно обновлен.")
                        return True
                    else:
                        logging.error(f"Ошибка обновления: API вернул ошибку. Ответ: {result}")
                        return False
                else:
                    logging.error(f"Ошибка обновления документа. Статус: {response.status}, Ответ: {await response.text()}")
                    return False
        except Exception as e:
            logging.error(f"Исключение при обновлении документа: {e}")
            return False


async def main():
    """Основная функция-оркестратор."""
    
    # --- ID документа в M.E.Doc, который нужно обновить ---
    TARGET_CARD_CODE = 583366  # Взять номер документа из накладной через F11

    # --- Параметры для выборки из БД ---
    buyer_okpo = "32490244"
    start_date = "01.12.2024"
    end_date = "01.01.2025"
    
    async with aiohttp.ClientSession() as session:
        # ЭТАП 1: Получаем текущие данные документа
        current_doc_data = await get_document_values(session, TARGET_CARD_CODE)
        if not current_doc_data:
            logging.error("Не удалось получить исходные данные документа. Обновление отменено.")
            return

        # Получаем актуальные данные из PostgreSQL для таблицы
        blocked_invoices = await get_blocked_invoices_from_db(DB_CONFIG, buyer_okpo, start_date, end_date)
        if not blocked_invoices:
            logging.warning("Не найдено заблокированных накладных. Табличная часть будет пустой.")
        
        # ЭТАП 2 и 3: Обновляем документ
        success = await update_document_with_header_fill(
            session=session,
            card_code=TARGET_CARD_CODE,
            current_doc_data=current_doc_data,
            blocked_invoices=blocked_invoices
        )
        
        if success:
            print(f"\nИтог: Документ J1312603 с CardCode = {TARGET_CARD_CODE} успешно обновлен в M.E.Doc.")
        else:
            print(f"\nИтог: Не удалось обновить документ J1312603 с CardCode = {TARGET_CARD_CODE}.")


if __name__ == '__main__':
    asyncio.run(main())