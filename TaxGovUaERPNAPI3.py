# -*- coding: utf-8 -*-

"""
Этот скрипт предназначен для асинхронной загрузки данных из API Tax Cabinet о ERPN (Електронний реєстр податкових накладних).
Он получает токен аутентификации, запрашивает данные за указанный период, сохраняет их в базу данных PostgreSQL
и опционально скачивает PDF-документы и квитанции.

Основные функции:
- Получение токена через Selenium.
- Асинхронные запросы к API для получения количества страниц и данных.
- Сохранение данных в таблицы t_tax_cabinet_erpn_api и t_tax_cabinet_erpn_api_block.
- Удаление дубликатов из базы данных.
- Скачивание PDF-документов и квитанций для заблокированных документов.

Требования:
- Установленные переменные окружения для БД.
- Наличие файла TaxGovUaConfig.py с функцией get_token().
- ChromeDriver для Selenium.

Использование:
- Запустите скрипт для загрузки данных за последние 18 месяцев.
"""

import asyncio
import time
from datetime import datetime
import os
import aiohttp
import requests  # вначале установите библиотеку grequests, потом requests
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta

from AsyncPostgresql import async_save_pg
from TaxGovUaConfig import get_token, remove_duplicates
from tables.t_tax_cabinet_erpn_api import SQL_INSERT_ERPN
from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_ADD_FROM_ERPN_API
from ChromeDriverUpdater import update_chromedriver_if_needed


# Обновляем ChromeDriver, если это необходимо
update_chromedriver_if_needed()
URL_MAIN = "https://cabinet.tax.gov.ua/ws/api"
URL_ERPN = f"{URL_MAIN}/nlnk/nlnkhd?fromImpdate="

driver, token = get_token()
wrong_urls = []


# Создаем url с датой начала и конца периода. Нужно для получения количества страниц
async def get_pages_urls(date_in, date_out):
    date_in = parse(date_in, dayfirst=False).date()
    date_out = parse(date_out, dayfirst=False).date()
    interval_days = (date_out - date_in).days
    return [
        (
            f"{URL_ERPN}{(date_in + relativedelta(days=i)).strftime('%Y-%m-%d')}%2000:00:00"
            f"&toImpdate={(date_in + relativedelta(days=i)).strftime('%Y-%m-%d')}%2023:59:59"
            "&sort=impdate"
            "&size=1000"
        )
        for i in range(interval_days + 1)
    ]


# получение количества страниц в данном периоде
async def get_page_count_async(url, session, semaphore, retries=3):
    global URL_ERPN, token, wrong_urls, driver
    async with semaphore:
        await asyncio.sleep(5)
        for attempt in range(retries):
            print(f"{datetime.now()}; url: {url}")
            if not token:
                if driver:
                    driver.quit()
                    driver = None
                driver, token = get_token()
                print(f"Update token: {datetime.now()}; {token}")
            headers = {"Authorization": token, "Content-Type": "application/json"}
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        result = await response.json()
                        return [url, result.get("totalPages")]
            except aiohttp.ClientError as e:
                print(f"Ошибка при запросе {url}: {e}")
                pass
            except asyncio.TimeoutError:
                # print(f"Тайм-аут при запросе данных: {url}")
                pass
            await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой
            _, token = get_token(driver=driver)

    wrong_urls.append(url)
    return [0, 0]


async def get_page_count(url, retries=3):
    global URL_ERPN, driver, token, wrong_urls
    response = ''
    for attempt in range(retries):
        response = None
        if not token:
            if driver:
                driver.quit()
                driver = None
            driver, token = get_token()
            print(f"Update token: {datetime.now()}; {token}")
        headers = {"Authorization": token, "Content-Type": "application/json"}
        try:
            response = requests.get(url, headers=headers, timeout=20)
            if response and response.status_code == 200:
                # print(f"url: {url}")
                result = response.json()
                return [url, result.get("totalPages")]
        except Exception as e:
            pass

        time.sleep(2 ** attempt)
        if driver and (not response or response.status_code != 200):
            driver.quit()
            driver = None
            token = None

    print(f"ERROR page url: {url}\n{response.text}")
    wrong_urls.append(url)
    return [0, 0]


# В файле TaxGovUaERPNAPI3.py, можно разместить перед функцией fetch

def download_and_save_pdf(url, headers, file_path, retries=3):
    """
    Скачивает PDF по указанному URL и сохраняет его в file_path.
    """
    try:
        # ИСПРАВЛЕНО: Создаем полную иерархию директорий для файла.
        # os.path.dirname(file_path) получает путь к папке из полного пути к файлу.
        # os.makedirs(...) создает все необходимые папки в этом пути.
        # exist_ok=True предотвращает ошибку, если папки уже существуют.
        file_dir = os.path.dirname(file_path)
        os.makedirs(file_dir, exist_ok=True)

    except Exception as e:
        print(f"Критическая ошибка: не удалось создать директорию {os.path.dirname(file_path)}. Ошибка: {e}")
        return False # Выходим, если не можем создать папку
        
    for attempt in range(retries):
        try:
            # Важно: stream=True позволяет скачивать большие файлы без загрузки всего содержимого в память
            response = requests.get(url, headers=headers, timeout=30, stream=True)
            if response.status_code == 200:
                # Открываем файл для записи в бинарном режиме ('wb')
                with open(file_path, 'wb') as f:
                    # Записываем содержимое файла по частям
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                print(f"Файл успешно сохранен: {file_path}")
                return True # Успешно скачали и сохранили
            else:
                print(f"Ошибка при скачивании {url}: Статус {response.status_code}, Ответ: {response.text}")

        except requests.exceptions.RequestException as e:
            print(f"Ошибка сети при попытке {attempt + 1} для {url}: {e}")
            time.sleep(2 ** attempt)
        except Exception as e:
            print(f"Неизвестная ошибка при попытке {attempt + 1} для {url}: {e}")
            time.sleep(2 ** attempt)
    
    print(f"Не удалось скачать файл после {retries} попыток: {url}")
    return False # Не удалось скачать


# Асинхронная функция для выполнения HTTP-запроса
async def fetch(url, retries=3):
    global driver, token, wrong_urls
    response = ''
    for attempt in range(retries):
        response = None
        if not token:
            if driver:
                driver.quit()
                driver = None
            driver, token = get_token()
            print(f"Update token: {datetime.now()}; {token}")
        headers = {"Authorization": token, "Content-Type": "application/json"}
        try:
            response = requests.get(url, headers=headers, timeout=20)
            if response and response.status_code == 200:
                # print(f"url: {url}")
                result = response.json()
                # if result and result.get("content"):
                #     record = result.get("content")[0]
                #     # определяем, заблокированы ли документы
                #     if 'зупинено' in record.get("hsmcsttName") \
                #         or 'вiдмовлено'  in record.get("hsmcsttName") \
                #         or 'скас' in record.get("hsmcsttName"):
                            
                #         impdata = record.get("impdate")
                #         code = record.get("code")
                #         customer_okpo = record.get("cpTin")
                #         if not customer_okpo or int(customer_okpo) == 0:
                #             print(f"Клиент {record.get('hnamebuy')} не имеет ОКПО. ")
                #         doc_date = parse(record.get("crtdate"), dayfirst=False).date() # преобразуем в datetime.date, чтобы убрать время
                #         period = doc_date.strftime('%Y%m')
                #         doc_date = doc_date.strftime('%d %m %Y')
                #         doc_number = record.get("nmr")
                #         cur_dir = os.path.dirname(os.path.abspath(__file__))
                #         save_to_dir = os.path.join(cur_dir, "downloads", str(customer_okpo), period, 'Податкова Накладна')
                        
                #         # Скачивание документа
                #         base_filename = f"ПН {doc_number} від {doc_date}.pdf"
                #         url_doc_pdf = f"{URL_MAIN}/file/nlnkhd/pdf?code={code}&impdate={impdata}"
                #         file_path_doc = os.path.join(save_to_dir, base_filename)                        
                #         download_and_save_pdf(url_doc_pdf, headers, file_path_doc)
                                                    
                #         # Скачивание квитанции 1
                #         base_filename = f"ПН {doc_number} від {doc_date} (квитанція 1).pdf"
                #         file_path_doc = os.path.join(save_to_dir, base_filename)                        
                #         url_doc_receipt_pdf = f"{URL_MAIN}/file/nlnkhd/pdf/kvt?code={code}&impdate={impdata}"
                #         download_and_save_pdf(url_doc_receipt_pdf, headers, file_path_doc)
                #     # print("\n--- ПРИМЕР ДАННЫХ ИЗ API ---")
                #     # print(result["content"][0])  # Печатаем первый документ
                #     # print("----------------------------\n")
                return result
        except Exception as e:
            pass

        time.sleep(2 ** attempt)
        if driver and (not response or response.status_code != 200):
            driver.quit()
            driver = None
            token = None

    # print(f"count wrong_urls: {len(wrong_urls)}; url: {url}")
    print(f"ERROR url: {url}\n{response.text}")
    wrong_urls.append(url)
    return None


# отбираем url с количеством страниц, у которых количество страниц больше 0
async def create_url_with_pages(urls_pages):
    return [[url, pages] for url, pages in urls_pages if pages > 0]


# Создаем url для периода с номером страницы.
# Количество url = количество страниц
async def create_urls(url_pages):
    all_urls = []
    for url, page_number in url_pages:
        urls = [f"{url}&page={page}" for page in range(page_number)]
        all_urls.extend(urls)
    return all_urls


# Асинхронная функция для выполнения HTTP-запроса
async def fetch_async(url, session, semaphore, retries=3):
    global driver, token, wrong_urls
    async with semaphore:
        await asyncio.sleep(2)
        headers = {"Authorization": token, "Content-Type": "application/json"}
        for attempt in range(retries):
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        return await response.json()
            except Exception as e:
                await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой
                _, token = get_token(driver=driver)

    wrong_urls.append(url)
    return None


# Асинхронная функция для запуска задач по 5 штук одновременно
async def run_tasks(urls, pages=False):
    semaphore = asyncio.Semaphore(5)  # Ограничение на 5 одновременных задач
    async with aiohttp.ClientSession() as session:
        if not pages:
            # tasks = [fetch_async(url, session, semaphore) for url in urls]
            tasks = [fetch(url) for url in urls]
        else:
            # tasks = [get_page_count_async(url, session, semaphore) for url in urls]
            tasks = [get_page_count(url) for url in urls]
        results = await asyncio.gather(*tasks)
    return results


# получаем content из json
async def get_content_from_response(responses):
    contents = [r.get("content") for r in responses if r]
    return [data_dict.values() for data_list in contents for data_dict in data_list]


# Преобразование данных в нужный формат
async def convert_data(data):
    data[3] = int(data[3]) if data[3] else 0  # Преобразование nmr в int
    data[11] = int(data[11]) if data[11] else 0  # Преобразование corrnmr в int
    data[4] = parse(data[4]) if data[4] else data[4]  # Время не отрезать. Используется в запросе
    data[13] = parse(data[13]) if data[13] else data[13] # Время не отрезать. Используется в запросе
    data[18] = parse(data[18]) if data[18] else data[18] # Время не отрезать. Используется в запросе
    return tuple(data)


async def main(date_from, date_to):
    global wrong_urls, driver, token
    urls = await get_pages_urls(date_from, date_to)
    urls_pages = await run_tasks(urls, pages=True)
    repeat_count = 1
    while len(wrong_urls) > 0:
        _, token = get_token(driver=driver)
        await asyncio.sleep(5)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL страниц: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        urls_pages.extend(await run_tasks(urls, pages=True))

    urls_pages_greater_0 = await create_url_with_pages(urls_pages)
    urls = await create_urls(urls_pages_greater_0)
    responses = await run_tasks(urls)
    repeat_count = 1
    while len(wrong_urls) > 0:
        _, token = get_token(driver=driver)
        await asyncio.sleep(5)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        responses.extend(await run_tasks(urls))

    # удаляем пустые значения из списка
    responses = [r for r in responses if r]
    datas = await get_content_from_response(responses)
    tasks = [convert_data(list(data)) for data in datas]
    data = await asyncio.gather(*tasks)
    result = await async_save_pg(SQL_INSERT_ERPN, data)
    print(f"Data saved: {result}")

    result = await async_save_pg(SQL_INSERT_ADD_FROM_ERPN_API)
    print(f"Data saved to t_tax_cabinet_erpn_api_block: {result}")

    result = remove_duplicates("t_tax_cabinet_erpn_api")
    print(f"Remove duplicates: {result}")


if __name__ == "__main__":
    print("Start", datetime.now())
    date_to = datetime.now().strftime("%Y-%m-%d")
    date_from = (datetime.now() - relativedelta(months=18)).replace(day=1).strftime("%Y-%m-%d")
    asyncio.run(main(date_from, date_to))
    driver.quit()
    print("End", datetime.now())
