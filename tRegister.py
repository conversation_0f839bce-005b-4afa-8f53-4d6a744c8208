import asyncio
from datetime import datetime, date

import pandas as pd

from AsyncHTTP import fetch_one_url
from AsyncPostgresql import async_save_pg, create_model_async
import os

hostname_public = os.getenv('MEDOC_API_HOST')

TAB_REGISTER = "medoc_reestr"
import pandas as pd

# Отобразить все столбцы
pd.set_option('display.max_columns', None)
# Отобразить все строки
pd.set_option('display.max_rows', None)

async def get_sql(columns_count):
    model = await create_model_async(columns_count)
    sql = f"""
            INSERT INTO {TAB_REGISTER}(
                status,
                vatsum,
                regdate,
                doc_id,
                exdoc_id,
                cardcode,
                doc_type,
                doc_move,
                doc_num,
                doc_vd,
                doc_dept,
                doc_date,
                firm_edrpou,
                firm_ipn,
                partner_edrpou,
                partner_dept,
                patrner_ipn,
                partner_name,
                osobfio,
                charcode,
                sender,
                reg_num,
                docsum,
                senduser,
                gettime,
                approvecode,
                expdate,
                notation,
                approve,
                dog_date, 
                dog_num
            )
            VALUES {model}            
        """
    return sql.replace("'", "")


SQL_DELETE_DOUBLE_OLD = f"""
    DELETE FROM {TAB_REGISTER} WHERE ctid NOT IN (SELECT MIN(ctid) FROM {TAB_REGISTER}
    GROUP BY 
        status,
        vatsum,
        regdate,
        doc_id,
        exdoc_id,
        cardcode,
        doc_type,
        doc_move,
        doc_num,
        doc_vd,
        doc_dept,
        doc_date,
        firm_edrpou,
        firm_ipn,
        partner_edrpou,
        partner_dept,
        patrner_ipn,
        partner_name,
        osobfio,
        charcode,
        sender,
        reg_num,
        docsum,
        senduser,
        gettime,
        approvecode,
        expdate,
        notation,
        approve,
        dog_date, 
        dog_num
    );
"""


SQL_DELETE_DOUBLE = f"""
-- А теперь сам запрос на удаление
DELETE FROM {TAB_REGISTER}
WHERE ctid IN (
    SELECT ctid
    FROM (
        SELECT 
            ctid,
            ROW_NUMBER() OVER (
                PARTITION BY 
                    status, vatsum, regdate, doc_id, exdoc_id, cardcode, doc_type, 
                    doc_move, doc_num, doc_vd, doc_dept, doc_date, firm_edrpou, 
                    firm_ipn, partner_edrpou, partner_dept, patrner_ipn, partner_name, 
                    osobfio, charcode, sender, reg_num, docsum, senduser, gettime, 
                    approvecode, expdate, notation, approve, dog_date, dog_num
                ORDER BY ctid ASC
            ) as rn
        FROM {TAB_REGISTER}
    ) t
    WHERE t.rn > 1
);
"""

async def save_register(reg_url) -> pd.DataFrame:
    # await clear_table(TAB_REGISTER)
    data = pd.DataFrame()
    source = await fetch_one_url(reg_url)
    if not source:
        return data
    data = pd.DataFrame(source)
    data.to_excel("register_export.xlsx", index=False)
    columns_count = len(source[0].keys())
    sql = await get_sql(columns_count)
    date_columns = ['regdate', 'doc_date', 'gettime', 'expdate', 'dog_date']

    for col in date_columns:
        data[col] = pd.to_datetime(data[col], format="%d.%m.%Y %H:%M:%S", errors='coerce')
        data[col].fillna(datetime.min, inplace=True)

    await async_save_pg(sql, data.values)
    await async_save_pg(SQL_DELETE_DOUBLE)
    # df = pd.DataFrame(source)
    return data


def save_register_sync(reg_url) -> pd.DataFrame:
    """Синхронная версия функции для сохранения регистра"""
    return asyncio.run(save_register(reg_url))


if __name__ == '__main__':
    start = datetime.now()
    print(start)
    date_first = "2024/09/01"
    date_last = "2024/09/30"  # date.today().strftime("%Y/%m/%d")
    urls = (
        f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg=781"
        f"&docType=-1&moveType=0&dateFrom={date_first}&dateEnd={date_last}"
    )
    asyncio.run(save_register(urls))
    print(datetime.now(), datetime.now() - start)
