import json
import time
import os
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from threading import Semaphore
from dotenv import load_dotenv
import requests
from dateutil.relativedelta import relativedelta
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()
hostname_public = os.getenv('MEDOC_API_HOST')

# Семафор для ограничения количества одновременных запросов
request_semaphore = Semaphore(5)


def check_server_availability():
    """Проверка доступности сервера перед началом работы."""
    try:
        response = requests.get(f"http://{hostname_public}:63777", timeout=5)
        return response.status_code == 200
    except Exception as e:
        print(f"Сервер недоступен: {e}")
        return False


def wait_for_server_availability(max_attempts=10, delay=30):
    """
    Ожидание доступности сервера с периодическими проверками.
    
    Args:
        max_attempts: Максимальное количество попыток
        delay: Задержка между попытками в секундах
    
    Returns:
        bool: True если сервер стал доступен, False если превышено количество попыток
    """
    for attempt in range(1, max_attempts + 1):
        print(f"Попытка {attempt}/{max_attempts} подключения к серверу...")
        
        if check_server_availability():
            print(f"Сервер {hostname_public}:63777 доступен!")
            return True
        
        if attempt < max_attempts:
            print(f"Сервер недоступен. Ожидание {delay} секунд перед следующей попыткой...")
            time.sleep(delay)
    
    print(f"Сервер {hostname_public}:63777 недоступен после {max_attempts} попыток")
    return False


def get_data_sync_from_url(url):
    """Синхронное получение данных - основной метод."""
    try:
        # Увеличиваем таймаут до 60 секунд
        response = requests.get(url, timeout=60)
        if response.status_code == 200:
            result = response.text
            if result:  # Проверка на пустой ответ
                return json.loads(result)
    except Exception as e:
        print(f"Ошибка при получении данных из url: {url} - {e}")
    return None


def fetch_url(url, count=1):
    """Синхронное получение данных с повторными попытками."""
    while count < 5:
        try:
            response = requests.get(url, timeout=60)
            if response.status_code == 200:
                result = response.text
                if result:  # Проверка на пустой ответ
                    return json.loads(result)
                print(f"Получен статус {response.status_code} для {url}")
        except requests.exceptions.Timeout:
            print(f"Попытка {count} - таймаут для {url}")
        except Exception as e:
            # Логируем конкретную ошибку для отладки
            print(f"Попытка {count} не удалась для {url}: {type(e).__name__}: {e}")
        
        # Используем обычный sleep
        sleep_time = min(5 * count, 30)  # Максимум 30 секунд между попытками
        print(f"Ожидание {sleep_time} секунд перед попыткой {count + 1}...")
        time.sleep(sleep_time)
        count += 1
    
    # Последняя попытка
    print(f"Последняя попытка для {url}")
    result = get_data_sync_from_url(url)
    if result:
        return result
    
    print(f"Не удалось получить данные из url: {url} после 5 попыток")
    return None


def fetch_one_url(url):
    """Получение данных из одного URL."""
    # Проверяем доступность сервера перед запросом
    if not check_server_availability():
        print(f"Сервер {hostname_public}:63777 недоступен")
        return None
    
    # Используем семафор для ограничения нагрузки
    with request_semaphore:
        response = fetch_url(url)
        return response


def fetch_url_with_semaphore(url):
    """Вспомогательная функция для работы с семафором."""
    with request_semaphore:
        return fetch_url(url)


def fetch_all_urls(urls):
    """Получение данных из множества URL с многопоточностью."""
    # Проверяем доступность сервера перед запросами
    if not check_server_availability():
        print(f"Сервер {hostname_public}:63777 недоступен")
        return [None] * len(urls)
    
    results = []
    
    # Используем ThreadPoolExecutor для параллельных запросов
    with ThreadPoolExecutor(max_workers=5) as executor:
        # Отправляем все задачи в пул потоков
        future_to_url = {executor.submit(fetch_url_with_semaphore, url): url for url in urls}
        
        # Получаем результаты по мере готовности
        for future in as_completed(future_to_url):
            url = future_to_url[future]
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                print(f"Ошибка при получении данных из {url}: {e}")
                results.append(None)
    
    return results


if __name__ == '__main__':
    # Сначала проверяем доступность сервера
    if not check_server_availability():
        print(f"ОШИБКА: Сервер {hostname_public}:63777 недоступен!")
        print("Проверьте:")
        print("1. Запущен ли сервер")
        print("2. Правильный ли адрес в переменной окружения PG_HOST")
        print("3. Доступен ли порт 63777")
        exit(1)
    
    print(f"Сервер {hostname_public}:63777 доступен")
    
    date_last = datetime.today().date().strftime("%Y/%m/%d")
    date_first = (datetime.today().date() - relativedelta(months=3)).strftime("%Y/%m/%d")
    url = (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg=781"
           f"&docType=-1&moveType=0&dateFrom={date_first}&dateEnd={date_last}")
    
    result = fetch_one_url(url)
    if result:
        print(f"Успешно получены данные: {len(str(result))} символов")
    else:
        print("Не удалось получить данные")