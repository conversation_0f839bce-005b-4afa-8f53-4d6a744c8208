import sys
from cx_Freeze import setup, Executable

# Dependencies are automatically detected, but it might need fine tuning.
build_options = {
    'packages': ['asyncio', 'aiohttp', 'fitz', 'dateutil', 'dotenv'],
    'excludes': [],
    'include_files': ['.env'] if sys.platform.startswith('win') else []
}

base = 'Console'

executables = [
    Executable('downloadPdf.py', base=base, target_name='MedocPdfDownloader.exe')
]

setup(
    name='MedocPdfDownloader',
    version='1.0',
    description='PDF Downloader for Medoc',
    options={'build_exe': build_options},
    executables=executables
)
