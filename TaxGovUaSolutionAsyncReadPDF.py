import os
from datetime import datetime
import asyncio
from dateutil.parser import parse
from pathlib import Path
import requests
from ReadPDFByPytesseractAsync import (
    extract_other_data,
    extract_date_short_and_number_async,
    remover_all_files_in_folder,
)
from AsyncPostgresql import (
    username,
    psw,
    basename,
    port,
    async_save_pg,
)

hostname_public = os.getenv('MEDOC_API_HOST')
from TaxGovUaConfig import get_token

url_const = r"https://cabinet.tax.gov.ua/ws/api/regdoc/doc/"
current_path = os.path.dirname(os.path.abspath(__file__))
download_path = os.path.join(current_path, "downloads", "pdf", "solution")
os.makedirs(download_path, exist_ok=True)

SQL_UPDATE = """
    UPDATE t_tax_cabinet_solutions
    SET doc_date = to_date($1, 'DD.MM.YYYY')::date,
        doc_number = $2,
        doc_type = $3,
        doc_sum = $4::numeric,
        doc_vat = $5::numeric,
        solution_date = to_date($6, 'DD.MM.YYYY')::date,
        solution_number = $7
    WHERE id = $8::numeric
    ;
"""


async def move_pdf_to_another_folder(pdf_file):
    archive_dir = os.path.join(download_path, "archive")
    os.makedirs(archive_dir, exist_ok=True)
    new_pdf_file = os.path.join(archive_dir, os.path.basename(pdf_file))
    os.rename(pdf_file, new_pdf_file)
    return new_pdf_file


async def save_data_to_db(data):
    result = await async_save_pg(SQL_UPDATE, data)
    if result:
        print(f"Данные сохранены в базу данных: {data[0][-1]}")
    return result


async def all_pdf_filenames_to_df():
    pdf_files_list_without_path = [
        file for file in os.listdir(download_path) if file.endswith(".pdf")
    ]
    return pdf_files_list_without_path


# Основная асинхронная функция для обработки PDF файлов с группировкой по 10 штук
async def main_solutions_pdf_files_group_async():
    pdf_files_list = await all_pdf_filenames_to_df()
    tasks = []
    for pdf_file in pdf_files_list:
        try:
            pdf_file_path = os.path.join(download_path, pdf_file)
            tasks.append(process_pdf_file(pdf_file_path))
        except Exception as e:
            # в случае, если файл обработан другим процессом, просто пропускаем его
            continue

        # если задачи запускаются по 10 штук
        # Если количество задач достигает 10, запускаем их и очищаем список задач
        if len(tasks) == 10:
            await asyncio.gather(*tasks)
            tasks = []

    # Запускаем оставшиеся задачи, если они есть
    if tasks:
        await asyncio.gather(*tasks)


# Основная асинхронная функция для обработки PDF файлов
async def main_solutions_pdf_files_async():
    pdf_files_list = await all_pdf_filenames_to_df()
    pdf_files_list.sort(reverse=True)
    tasks = []
    for pdf_file in pdf_files_list:
        try:
            pdf_file_path = os.path.join(download_path, pdf_file)
            tasks.append(process_pdf_file(pdf_file_path))

            # Если количество задач достигает 5, запускаем их и очищаем список задач
            if len(tasks) == 15:
                await asyncio.gather(*tasks)
                tasks = []
        except Exception as e:
            # В случае, если файл обработан другим процессом, просто пропускаем его
            print(f"Ошибка при обработке файла {pdf_file}: {e}")
            continue

    # Запускаем оставшиеся задачи, если они есть
    if tasks:
        await asyncio.gather(*tasks)


# Асинхронная функция для обработки одного PDF файла
async def process_pdf_file(pdf_file_path):
    await remover_all_files_in_folder(".ppm")
    if not os.path.exists(pdf_file_path):
        # если файл обработан другим процессом, просто пропускаем его
        return None
    data = await extract_other_data(pdf_file_path)
    if not data:
        return None
    elif 'таблица' in data:
        new_pdf_file = pdf_file_path.replace(f"{Path(pdf_file_path).stem}", f"{Path(pdf_file_path).stem} {data}")
        os.rename(pdf_file_path, new_pdf_file)
        await move_pdf_to_another_folder(new_pdf_file)
    elif len(data) == 8:
        data = tuple(data)
        result = await save_data_to_db([data])
        # print(f"Результат сохранения данных в базу: {result}")
        if result:
            await move_pdf_to_another_folder(pdf_file_path)


if __name__ == "__main__":
    print("Start", datetime.now())
    asyncio.run(main_solutions_pdf_files_async())
    print("End", datetime.now())
