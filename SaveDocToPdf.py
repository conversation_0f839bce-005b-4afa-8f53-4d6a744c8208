# SaveDocToPdf.py - Оптимизированная версия

import asyncio
import base64
import os
import logging
from typing import Optional, Tuple
import time

import pandas as pd
from transliterate import translit

from AsyncHTTP import fetch_one_url, check_server_availability, wait_for_server_availability
from AsyncPostgresql import engine, async_save_pg
from ReadPDFByPDFLumber import extract_column_data_with_laparams
from ReadPDFGetAccountNumber import find_account_numbers
from ReadPDFMiner import extract_column_with_pdfminer
from SendMail import send_email
from dotenv import load_dotenv
load_dotenv()

hostname_public = os.getenv('MEDOC_API_HOST')

# Настройка логирования
logger = logging.getLogger(__name__)

TABLE_NAME = 't_medoc_sent_document'

SQL_CREATE_TABLE = f"""
    CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
        doc_id varchar(40) NOT NULL,
        customer_edrpou varchar(15) DEFAULT NULL,
        customer_name varchar(100) DEFAULT NULL,
        customer_account varchar(30) DEFAULT NULL,
        file_name varchar(100) NOT NULL,
        doc_number varchar(20) DEFAULT NULL,
        doc_date date DEFAULT NULL,
        amount numeric(15,2) DEFAULT 0,
        amount_vat numeric(15,2) DEFAULT 0,
        manager_email varchar(25) DEFAULT NULL,
        payment_purpose varchar(300) DEFAULT NULL,
        date_send timestamp with time zone DEFAULT now(),        
        CONSTRAINT {TABLE_NAME}_pk PRIMARY KEY (doc_id)
    );

    COMMENT ON TABLE {TABLE_NAME} IS 'docid отправленных на почту менеджерам документов';
    COMMENT ON COLUMN {TABLE_NAME}.doc_id IS 'id документа';
    COMMENT ON COLUMN {TABLE_NAME}.customer_edrpou IS 'код контрагента';
    COMMENT ON COLUMN {TABLE_NAME}.customer_name IS 'наименование контрагента';    
    COMMENT ON COLUMN {TABLE_NAME}.file_name IS 'Имя файла';
    COMMENT ON COLUMN {TABLE_NAME}.doc_number IS 'номер документа';
    COMMENT ON COLUMN {TABLE_NAME}.doc_date IS 'дата документа';
    COMMENT ON COLUMN {TABLE_NAME}.amount IS 'сумма документа';
    COMMENT ON COLUMN {TABLE_NAME}.amount_vat IS 'сумма НДС';
    COMMENT ON COLUMN {TABLE_NAME}.manager_email IS 'email менеджера';
    COMMENT ON COLUMN {TABLE_NAME}.date_send IS 'дата отправки email';
    COMMENT ON COLUMN {TABLE_NAME}.payment_purpose IS 'назначение платежа';
    COMMENT ON COLUMN {TABLE_NAME}.customer_account IS 'расчетный счет контрагента';
"""

SQL_INSERT_DOCID = f"""
    INSERT INTO {TABLE_NAME} 
    (
        doc_id, 
        customer_edrpou,
        customer_name, 
        customer_account,
        file_name, 
        doc_number, 
        doc_date, 
        amount, 
        amount_vat,
        manager_email,
        payment_purpose
    ) 
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    ON CONFLICT (doc_id) DO NOTHING
    ;
"""

# Конфигурация повторных попыток
MAX_RETRIES = 3
RETRY_DELAY = 2


async def create_table():
    """Создание таблицы с повторными попытками."""
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            await async_save_pg(SQL_CREATE_TABLE)
            return
        except Exception as e:
            logger.error(f"Error creating table (attempt {attempt}): {e}")
            if attempt < MAX_RETRIES:
                await asyncio.sleep(RETRY_DELAY * attempt)
            else:
                raise


def cyrillic_to_latin(text):
    return translit(text, 'ru', reversed=True)


async def get_unsent_data():
    """Получение неотправленных документов с обработкой ошибок."""
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            df = pd.read_sql(
                f"""
                    SELECT DISTINCT
                        * 
                    FROM v_medoc_reestr_and_docinfo 
                    WHERE doc_date::date >= '01.12.2024'::date
                        AND doc_type_name IN ('Счет','Взаимозачет')
                        AND doc_id NOT IN (SELECT doc_id FROM {TABLE_NAME})
                    ;
                    """,
                engine)
            return df
        except Exception as e:
            logger.error(f"Error getting unsent data (attempt {attempt}): {e}")
            if attempt < MAX_RETRIES:
                await asyncio.sleep(RETRY_DELAY * attempt)
            else:
                logger.critical("Failed to get unsent data after all attempts")
                return pd.DataFrame()  # Возвращаем пустой DataFrame


def get_manager_info(client_code):
    """Извлечение информации о менеджере с обработкой ошибок."""
    try:
        df = pd.read_sql(
            f"""
                SELECT DISTINCT
                    customer,
                    a_comment as email 
                FROM v_one_manager_counterparty_contracts_segments 
                WHERE edrpou = '{client_code}'
                ;
                """,
            engine)

        if df.empty:
            return None, None
        return df['customer'].values[0], df['email'].values[0]
    except Exception as e:
        logger.error(f"Error getting manager info for {client_code}: {e}")
        return None, None


def get_customer_accounts():
    """Получение счетов клиента с обработкой ошибок."""
    try:
        df = pd.read_sql(
            f"""
            SELECT 
                account_number as account
            FROM t_one_cat_cash_bank_accounts AS accounts
                INNER JOIN
                (
                    SELECT
                        primary_bank_account_key 
                    FROM public.t_one_cat_organizations x
                    WHERE primary_bank_account_key <> '********-0000-0000-0000-************'
                ) AS customer
                    ON customer.primary_bank_account_key = accounts.ref_key
            """,
            engine)

        if df.empty:
            return None
        return ' '.join(df['account'].values)
    except Exception as e:
        logger.error(f"Error getting customer accounts: {e}")
        return None


def delete_file(file_name):
    try:
        os.remove(file_name)
    except FileNotFoundError:
        pass
    except Exception as e:
        logger.error(f"Error deleting file {file_name}: {e}")


def send_mail_with_attachment(customer_edrpou, customer_name, to_list, file_name):
    """Отправка email с повторными попытками."""
    file_path = os.path.abspath(file_name)
    sender_email = "<EMAIL>"
    sender_name = "PrestigeBot"
    subject = f"{customer_edrpou} {file_name}"
    body = f'ОКПО {customer_edrpou}\n{customer_name}\n{file_name} во вложении.'

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            return send_email(sender_email, sender_name, to_list, subject, body, file_path, file_name)
        except Exception as e:
            logger.error(f"Error sending email to {to_list} (attempt {attempt}): {e}")
            if attempt < MAX_RETRIES:
                time.sleep(RETRY_DELAY * attempt)
            else:
                return False


async def process_document(row, doc_index):
    """Обработка одного документа с полной обработкой ошибок."""
    doc_id = row.get('doc_id')

    try:
        # Проверяем доступность сервера перед получением PDF
        if not await check_server_availability():
            logger.warning(f"Сервер недоступен для получения PDF документа {doc_id}")
            return False

        # Получаем PDF документа
        url = f'http://{hostname_public}:63777/api/Info/PrintDocPDF?idOrg=781&docID={doc_id}&facsimile=true'
        response = await fetch_one_url(url)

        if not response:
            logger.error(f"Failed to get PDF for doc_id: {doc_id}")
            return False

        file_name = response[0].get('FileName')
        file_raw = response[0].get('File')
        customer_name_in = row.get('partner_name')
        customer_edrpou = row.get('partner_edrpou')
        doc_date = row.get('doc_date')
        doc_number = row.get('doc_num')
        amount = row.get('docsum')
        amount_vat = row.get('vatsum')

        # Получаем информацию о менеджере
        customer_name, manager_email = get_manager_info(customer_edrpou)
        if customer_name_in:
            customer_name = customer_name_in

        # Декодирование файла из base64
        try:
            file_data = base64.b64decode(file_raw)
        except Exception as e:
            logger.error(f"Error decoding base64 for doc_id {doc_id}: {e}")
            return False

        # Запись файла с повторными попытками
        for attempt in range(1, MAX_RETRIES + 1):
            try:
                with open(file_name, 'wb') as f:
                    f.write(file_data)
                break
            except Exception as e:
                logger.error(f"Error writing file {file_name} (attempt {attempt}): {e}")
                if attempt == MAX_RETRIES:
                    return False
                await asyncio.sleep(RETRY_DELAY)

        # Извлечение информации из PDF
        exclude_accounts = get_customer_accounts()

        try:
            account_number = find_account_numbers(file_name, exclude_accounts)
        except Exception as e:
            logger.error(f"Error finding account numbers in {file_name}: {e}")
            account_number = None

        # Извлечение назначения платежа
        begin_word = ''
        finish_word = ''
        column_name = '№'
        payment_purpose_raw = None
        payment_purpose = None

        try:
            payment_purpose_raw = extract_column_data_with_laparams(file_name, column_name)
            if payment_purpose_raw:
                begin_word = payment_purpose_raw[:5]
                finish_word = payment_purpose_raw[-5:]
            elif customer_edrpou == '********':
                column_name = 'Абонплата'
        except Exception as e:
            logger.error(f"Error extracting column data from {file_name}: {e}")

        try:
            payment_purpose = extract_column_with_pdfminer(file_name, column_name, begin_word, finish_word)
        except Exception as e:
            logger.error(f"Error extracting with pdfminer from {file_name}: {e}")

        if payment_purpose:
            purpose_payment = payment_purpose
        else:
            purpose_payment = payment_purpose_raw

        if purpose_payment:
            purpose_payment = purpose_payment.replace('\n', '; ')

        # Отправка email
        if manager_email:
            to_list = [manager_email, '<EMAIL>']
            email_sent = False

            for email in to_list:
                is_sent = send_mail_with_attachment(customer_edrpou, customer_name, email, file_name)
                if is_sent:
                    email_sent = True
                else:
                    logger.error(f'Ошибка отправки файла {file_name} на почту {email}')

            if not email_sent:
                logger.warning(f"Failed to send {file_name} to any email address")

        # Сохранение в БД
        data = (
            doc_id,
            customer_edrpou,
            customer_name,
            account_number,
            file_name,
            doc_number,
            doc_date,
            amount,
            amount_vat,
            manager_email,
            purpose_payment
        )

        is_saved = await async_save_pg(SQL_INSERT_DOCID, [data])
        if not is_saved:
            logger.info(f'Документ {file_name} уже есть в таблице {TABLE_NAME}')

        logger.info(f'{doc_index}: Файл {file_name} сохранен')

        # Удаление файла (опционально)
        # delete_file(file_name)

        return True

    except Exception as e:
        logger.error(f"Unexpected error processing doc_id {doc_id}: {e}")
        return False


async def main_medoc_save_doc_to_pdf():
    """Основная функция с улучшенной обработкой ошибок и параллельной обработкой."""
    # Проверяем доступность сервера перед началом работы
    logger.info(f"Проверка доступности сервера {hostname_public}:63777...")
    if not await check_server_availability():
        logger.warning(f"Сервер {hostname_public}:63777 недоступен. Ожидание...")
        # Ждем доступности сервера
        if not await wait_for_server_availability(max_attempts=5, delay=20):
            logger.error(f"Сервер {hostname_public}:63777 недоступен для отправки PDF")
            return

    await create_table()

    df = await get_unsent_data()
    total_docs = len(df)
    logger.info(f'Количество документов для отправки: {total_docs}')

    if total_docs == 0:
        return

    # Создаем семафор для ограничения до 5 одновременных операций
    semaphore = asyncio.Semaphore(5)

    async def process_document_with_semaphore(row, doc_index):
        async with semaphore:
            return await process_document(row, doc_index)

    # Создаем все задачи
    tasks = []
    for idx, (_, row) in enumerate(df.iterrows()):
        doc_index = idx + 1
        task = process_document_with_semaphore(row, doc_index)
        tasks.append(task)

    # Выполняем все задачи с семафором
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Подсчитываем успешные
    successful = sum(1 for r in results if r is True)
    failed = sum(1 for r in results if isinstance(r, Exception) or r is False)

    logger.info(f"Обработка завершена: {successful}/{total_docs} документов успешно обработано, {failed} с ошибками")


if __name__ == '__main__':
    asyncio.run(main_medoc_save_doc_to_pdf())