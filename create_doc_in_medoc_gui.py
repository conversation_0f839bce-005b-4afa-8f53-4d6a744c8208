# -*- coding: utf-8 -*-
"""
Полный профессиональный скрипт:
- GUI для безопасного полного перезаписывания документа J1312603 в M.E.Doc по CardCode.
- Исправлено дублирование первой строки (LINE 0-based).
- Явно записываются поля HNUM/HDAT и N4 (дата) / N5 (номер).
- Добавлено поле выбора типа документа (НН=0, РК=1) — передаётся в SQL (ftype).
- Используется пул asyncpg.create_pool во всех обращениях к PostgreSQL (устойчивость соединения).
- Горячие клавиши Ctrl+C / Ctrl+V / Ctrl+X для всех полей ввода и контекстное меню сохранено.
- Логи минимальны и информативны: количество записей и сумма НДС, путь к бэкапу, итог обновления.
"""

import asyncio
import os
import json
import logging
import sys
import queue
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
from datetime import datetime
from typing import Optional, List, Dict, Any
from dotenv import load_dotenv
import aiohttp
import asyncpg
from tkcalendar import DateEntry

# ========== Настройка ==========
load_dotenv()
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# ========== Конфигурация ==========
HOSTNAME_PUBLIC = os.getenv("PG_HOST_LOCAL", "*************")
SEMAPHORE = asyncio.Semaphore(5)
DB_CONFIG = {
    "user": os.getenv("PG_USER"),
    "password": os.getenv("PG_PASSWORD"),
    "host": os.getenv("PG_HOST_LOCAL", "localhost"),
    "port": os.getenv("PG_PORT", 5432),
    "database": os.getenv("PG_DBNAME"),
}

# Источник данных для шапки документа (F11)
SOURCE_HEADER_DATA = {
    "FIRM_DPACD": "1305",
    "FIRM_DPANM": "ГОЛОВНЕ УПРАВЛІННЯ ДПС У ЛЬВІВСЬКІЙ ОБЛАСТІ, ЛЬВІВСЬКА ДЕРЖАВНА ПОДАТКОВА ІНСПЕКЦІЯ",
    "FIRM_EDRPOU": "41098985",
    "FIRM_NAME": 'ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ "ПРЕСТИЖ ПРОДУКТ.К"',
    "RUK": "Валяєв Расім Сейранович",
    "RUKINN": "2634316155",
}


# ==========================
# --- БЭКЕНД-ЛОГИКА ---
# ==========================

async def get_document_values(session: aiohttp.ClientSession, card_code: int) -> Optional[List[Dict[str, Any]]]:
    """Получить текущее содержимое документа для бэкапа."""
    url = f"http://{HOSTNAME_PUBLIC}:63777/api/Document/GetValues"
    params = {"cardCode": card_code}
    try:
        async with session.get(url, params=params) as response:
            if response.status == 200:
                return await response.json()
            else:
                print(f"ОШИБКА: Не удалось получить данные документа. Статус: {response.status}")
                return None
    except Exception as e:
        print(f"КРИТИЧЕСКАЯ ОШИБКА при получении данных документа: {e}")
        return None


async def get_blocked_invoices_from_db(config: Dict, buyer_okpo: str, start_date: str, end_date: str, ftype: int) -> List[Dict[str, Any]]:
    """Извлекает данные о заблокированных НН/РК из PostgreSQL (с учётом ftype).
       Использует пул соединений asyncpg.create_pool для устойчивости.
    """
    sql_query = """
    SELECT 
        ROW_NUMBER() OVER (ORDER BY crtdate, nmr) AS row_no,
        crtdate, nmr, docrnn, pkpinn, hnamebuy,
        r4100g11::numeric AS r4100g11,
        ndssm::numeric AS ndssm
    FROM public.t_tax_cabinet_erpn_api
    WHERE 
        crtdate >= $1 AND crtdate <= $2
        AND (hsmcstt IN (1, 2, 13, 14, 15, 18) OR hsmcsttname ILIKE ANY (ARRAY['%вiдмовлено%','%зупинено%']))
        AND ftype = $3 AND cptin = $4::numeric
    ORDER BY crtdate, nmr;
    """
    try:
        start_date_obj = datetime.strptime(start_date, "%d.%m.%Y")
        end_date_obj = datetime.strptime(end_date, "%d.%m.%Y")

        # Пул: создаётся и используется в контексте — безопасно и нормально для параллельных вызовов
        async with asyncpg.create_pool(**config, min_size=1, max_size=5) as pool:
            async with pool.acquire() as conn:
                records = await conn.fetch(sql_query, start_date_obj, end_date_obj, ftype, buyer_okpo)

        total_vat = sum(rec["ndssm"] or 0 for rec in records)
        print(f"Получено {len(records)} записей из БД. Общая сумма НДС: {total_vat:.2f}")
        return [dict(rec) for rec in records]
    except Exception as e:
        print(f"ОШИБКА при работе с PostgreSQL: {e}")
        return []


async def update_document(session: aiohttp.ClientSession, card_code: int, payload: List[Dict[str, Any]]) -> bool:
    """Отправляет данные для полной перезаписи документа в M.E.Doc."""
    url = f"http://{HOSTNAME_PUBLIC}:63777/api/Document/SetValues"
    # Привести VALUE к строкам — API стабильнее принимает строки
    for item in payload:
        if "VALUE" in item and item["VALUE"] is not None:
            item["VALUE"] = str(item["VALUE"])
    params = {"cardCode": card_code, "overwrite": "true"}
    try:
        async with SEMAPHORE:
            async with session.post(url, params=params, json=payload) as response:
                if response.status == 200:
                    # Пробуем разобрать JSON-ответ
                    try:
                        result = await response.json()
                        if isinstance(result, dict) and result.get("Code") == 0:
                            print(f"✅ УСПЕХ! Документ CardCode={card_code} успешно обновлен.")
                            return True
                        else:
                            print(f"ОШИБКА ОБНОВЛЕНИЯ: {result}")
                            return False
                    except Exception:
                        # Если не JSON, но 200 — считаем успешным (редкий кейс)
                        print(f"✅ УСПЕХ! Документ CardCode={card_code} успешно обновлен. (нет JSON-ответа)")
                        return True
                else:
                    print(f"ОШИБКА ОБНОВЛЕНИЯ: Статус: {response.status}")
                    return False
    except Exception as e:
        print(f"КРИТИЧЕСКАЯ ОШИБКА при обновлении документа: {e}")
        return False


# ==========================
# --- GUI / UI logic ---
# ==========================

class QueueWriter:
    def __init__(self, queue): self.queue = queue
    def write(self, text):
        # Потокобезопасная отправка в очередь логов
        try:
            self.queue.put(str(text))
        except Exception:
            pass
    def flush(self): pass


class App:
    def __init__(self, root):
        self.root = root
        self.root.title("Обновление документа J1312603 в M.E.Doc")
        self.root.geometry("980x720")
        self.root.minsize(800, 600)

        self.db_data: Optional[List[Dict[str, Any]]] = None
        self.current_card_code: Optional[int] = None

        style = ttk.Style(self.root)
        style.theme_use("clam")
        style.configure("TLabel", padding=5, font=("Helvetica", 10))
        style.configure("TEntry", padding=5, font=("Helvetica", 10))
        style.configure("TButton", padding=5, font=("Helvetica", 10))
        style.configure("Treeview.Heading", font=("Helvetica", 10, "bold"))

        # --- Input frame ---
        input_frame = ttk.LabelFrame(self.root, text="Параметры", padding="10")
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        input_frame.columnconfigure(1, weight=1); input_frame.columnconfigure(3, weight=1)

        # CardCode
        ttk.Label(input_frame, text="CardCode документа:").grid(row=0, column=0, sticky=tk.W)
        self.card_code_entry = ttk.Entry(input_frame)
        self.card_code_entry.grid(row=0, column=1, sticky=tk.EW, padx=(0, 10))
        self.add_context_menu(self.card_code_entry)

        # OKPO
        ttk.Label(input_frame, text="ОКПО Покупателя:").grid(row=1, column=0, sticky=tk.W)
        self.buyer_okpo_entry = ttk.Entry(input_frame)
        self.buyer_okpo_entry.grid(row=1, column=1, sticky=tk.EW, padx=(0, 10))
        self.add_context_menu(self.buyer_okpo_entry)

        # Тип документа (добавлено)
        ttk.Label(input_frame, text="Тип документа:").grid(row=2, column=0, sticky=tk.W)
        self.ftype_combo = ttk.Combobox(input_frame, values=["НН (0)", "РК (1)"], state="readonly", width=14)
        self.ftype_combo.current(0)
        self.ftype_combo.grid(row=2, column=1, sticky=tk.W, padx=(0, 10))

        # Дата с (БД)
        ttk.Label(input_frame, text="Дата с (БД):").grid(row=0, column=2, sticky=tk.W)
        self.date_from_entry = DateEntry(input_frame, date_pattern="dd.mm.y", width=18, locale="uk_UA")
        self.date_from_entry.grid(row=0, column=3, sticky=tk.EW, padx=(0, 10))

        # Дата по (БД)
        ttk.Label(input_frame, text="Дата по (БД):").grid(row=1, column=2, sticky=tk.W)
        self.date_to_entry = DateEntry(input_frame, date_pattern="dd.mm.y", width=18, locale="uk_UA")
        self.date_to_entry.grid(row=1, column=3, sticky=tk.EW, padx=(0, 10))

        # Номер/дата повідомлення
        ttk.Label(input_frame, text="Номер повідомлення:").grid(row=2, column=2, sticky=tk.W)
        self.doc_num_entry = ttk.Entry(input_frame)
        self.doc_num_entry.grid(row=2, column=3, sticky=tk.EW)
        self.add_context_menu(self.doc_num_entry)

        ttk.Label(input_frame, text="Дата повідомлення:").grid(row=3, column=2, sticky=tk.W)
        self.doc_date_entry = DateEntry(input_frame, date_pattern="dd.mm.y", width=18, locale="uk_UA")
        self.doc_date_entry.grid(row=3, column=3, sticky=tk.EW)

        # Кнопка подготовки
        self.fetch_button = ttk.Button(input_frame, text="Подготовить данные для обновления", command=self.start_fetch_thread)
        self.fetch_button.grid(row=4, column=0, columnspan=4, sticky=tk.EW, pady=(10, 0))

        # --- Preview frame ---
        preview_frame = ttk.LabelFrame(self.root, text="Данные для обновления (из базы данных)", padding="10")
        preview_frame.pack(expand=True, fill=tk.BOTH, padx=10, pady=5)
        self.tree_new = self.create_preview_table(preview_frame)

        # --- Action buttons ---
        action_frame = ttk.Frame(self.root, padding="10")
        action_frame.pack(fill=tk.X, padx=10, pady=5)
        self.update_button = ttk.Button(action_frame, text="Выполнить обновление", command=self.start_update_thread, state=tk.DISABLED)
        self.update_button.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        self.restore_button = ttk.Button(action_frame, text="Восстановить из резервной копии...", command=self.start_restore_thread)
        self.restore_button.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)

        # --- Log frame ---
        log_frame = ttk.LabelFrame(self.root, text="Лог выполнения", padding="10")
        log_frame.pack(expand=True, fill=tk.BOTH, padx=10, pady=10, ipady=50)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, font=("Courier New", 9))
        self.log_text.pack(expand=True, fill=tk.BOTH)
        self.add_context_menu(self.log_text)

        # Лог-очередь
        self.log_queue: queue.Queue = queue.Queue()
        self.root.after(100, self.process_queue)

    # -----------------------
    # Контекстное меню и хоткеи
    # -----------------------
    def add_context_menu(self, widget):
        menu = tk.Menu(widget, tearoff=0)
        menu.add_command(label="Вырезать", command=lambda w=widget: self._do_cut(w))
        menu.add_command(label="Копировать", command=lambda w=widget: self._do_copy(w))
        menu.add_command(label="Вставить", command=lambda w=widget: self._do_paste(w))
        widget.bind("<Button-3>", lambda e: menu.post(e.x_root, e.y_root))

        # Горячие клавиши — привязаны к виджету. Возвращаем "break" чтобы не дублировать
        widget.bind("<Control-c>", lambda e, w=widget: (self._do_copy(w), "break"))
        widget.bind("<Control-C>", lambda e, w=widget: (self._do_copy(w), "break"))
        widget.bind("<Control-x>", lambda e, w=widget: (self._do_cut(w), "break"))
        widget.bind("<Control-X>", lambda e, w=widget: (self._do_cut(w), "break"))
        widget.bind("<Control-v>", lambda e, w=widget: (self._do_paste(w), "break"))
        widget.bind("<Control-V>", lambda e, w=widget: (self._do_paste(w), "break"))

    def _do_copy(self, widget):
        try:
            sel = widget.selection_get()
            widget.clipboard_clear()
            widget.clipboard_append(sel)
        except Exception:
            pass

    def _do_cut(self, widget):
        try:
            sel = widget.selection_get()
            widget.clipboard_clear()
            widget.clipboard_append(sel)
            # удаляем выделение
            try:
                widget.delete("sel.first", "sel.last")
            except Exception:
                pass
        except Exception:
            pass

    def _do_paste(self, widget):
        try:
            clip = widget.clipboard_get()
            if isinstance(widget, (tk.Text, scrolledtext.ScrolledText)):
                widget.insert(tk.INSERT, clip)
            else:
                # для Entry: заменяем выделение или вставляем в курсор
                try:
                    widget.delete("sel.first", "sel.last")
                except Exception:
                    pass
                try:
                    widget.insert(tk.INSERT, clip)
                except Exception:
                    # fallback: вставить в конец
                    try:
                        widget.insert(tk.END, clip)
                    except Exception:
                        pass
        except Exception:
            pass

    # -----------------------
    # UI вспомогательные
    # -----------------------
    def create_preview_table(self, parent):
        cols = ("№", "Дата", "Номер", "Рег. номер", "Покупатель", "Сумма без НДС", "Сумма НДС")
        tree = ttk.Treeview(parent, columns=cols, show="headings")
        for col in cols:
            tree.heading(col, text=col)
        tree.column("№", width=40, anchor=tk.CENTER, stretch=False)
        tree.column("Дата", width=90, stretch=False)
        tree.column("Номер", width=80, stretch=False)
        tree.column("Рег. номер", width=100, stretch=False)
        tree.column("Покупатель", width=300)
        tree.column("Сумма без НДС", width=130, anchor=tk.E)
        tree.column("Сумма НДС", width=110, anchor=tk.E)
        vsb = ttk.Scrollbar(parent, orient="vertical", command=tree.yview)
        vsb.pack(side="right", fill="y")
        tree.configure(yscrollcommand=vsb.set)
        tree.pack(expand=True, fill=tk.BOTH)
        return tree

    def set_ui_state(self, is_busy: bool):
        state = tk.DISABLED if is_busy else tk.NORMAL
        widgets = [
            self.fetch_button, self.restore_button,
            self.card_code_entry, self.buyer_okpo_entry,
            self.date_from_entry, self.date_to_entry,
            self.doc_num_entry, self.doc_date_entry, self.ftype_combo
        ]
        for w in widgets:
            try:
                w.config(state=state)
            except Exception:
                pass
        if is_busy:
            try:
                self.update_button.config(state=tk.DISABLED)
            except Exception:
                pass

    # -----------------------
    # Fetch (поиск данных и заполнение preview)
    # -----------------------
    def start_fetch_thread(self):
        card_code_str = self.card_code_entry.get().strip()
        buyer_okpo = self.buyer_okpo_entry.get().strip()
        date_from = self.date_from_entry.get_date()
        date_to = self.date_to_entry.get_date()
        ftype_val = 0 if self.ftype_combo.current() == 0 else 1

        if not all([card_code_str, buyer_okpo]):
            messagebox.showerror("Ошибка", "Поля 'CardCode' и 'ОКПО Покупателя' должны быть заполнены!")
            return
        if not card_code_str.isdigit():
            messagebox.showerror("Ошибка", "CardCode должен быть числом.")
            return
        if date_from > date_to:
            messagebox.showerror("Ошибка", "Дата 'с' не может быть позже даты 'по'.")
            return

        self.current_card_code = int(card_code_str)
        self.db_data = None
        self.set_ui_state(is_busy=True)
        self.log_text.configure(state="normal"); self.log_text.delete(1.0, tk.END); self.log_text.configure(state="disabled")
        self.tree_new.delete(*self.tree_new.get_children())

        # Запускаем фоновую задачу
        threading.Thread(target=self.run_fetch_task, args=(buyer_okpo, date_from.strftime("%d.%m.%Y"), date_to.strftime("%d.%m.%Y"), ftype_val), daemon=True).start()

    def run_fetch_task(self, buyer_okpo, date_from, date_to, ftype):
        original_stdout = sys.stdout
        sys.stdout = QueueWriter(self.log_queue)
        try:
            self.db_data = asyncio.run(get_blocked_invoices_from_db(DB_CONFIG, buyer_okpo, date_from, date_to, ftype))
            self.log_queue.put(("FETCH_COMPLETE", self.db_data))
        except Exception as e:
            print(f"КРИТИЧЕСКАЯ ОШИБКА во время выборки: {e}")
            self.log_queue.put(("TASK_FAILED", None))
        finally:
            sys.stdout = original_stdout

    # -----------------------
    # Update (создание бэкапа и отправка нового тела документа)
    # -----------------------
    def start_update_thread(self):
        if self.db_data is None or self.current_card_code is None:
            messagebox.showerror("Ошибка", "Сначала необходимо найти и подготовить данные.")
            return

        doc_num = self.doc_num_entry.get().strip()
        try:
            doc_date_str = self.doc_date_entry.get_date().strftime("%d.%m.%Y")
        except Exception:
            doc_date_str = self.doc_date_entry.get().strip()

        if messagebox.askyesno("Подтверждение", f"Вы уверены, что хотите обновить документ с CardCode {self.current_card_code}?\n\nБудет создана резервная копия.\nТекущие данные в документе будут ПОЛНОСТЬЮ ПЕРЕЗАПИСАНЫ."):
            self.set_ui_state(is_busy=True)
            threading.Thread(target=self.run_update_task, args=(doc_num, doc_date_str), daemon=True).start()

    def run_update_task(self, doc_num, doc_date):
        original_stdout = sys.stdout
        sys.stdout = QueueWriter(self.log_queue)
        try:
            asyncio.run(self.async_update_logic(doc_num, doc_date))
            self.log_queue.put(("TASK_COMPLETE", None))
        except Exception as e:
            print(f"КРИТИЧЕСКАЯ ОШИБКА во время обновления: {e}")
            self.log_queue.put(("TASK_FAILED", None))
        finally:
            sys.stdout = original_stdout

    async def async_update_logic(self, doc_num, doc_date):
        async with aiohttp.ClientSession() as session:
            # Получаем текущее содержимое документа для бэкапа
            current_doc_data = await get_document_values(session, self.current_card_code)
            if not current_doc_data:
                print("Итог: Не удалось получить исходные данные для бэкапа. Обновление отменено.")
                return

            # Создаём резервную копию
            backup_dir = "backups"
            os.makedirs(backup_dir, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(backup_dir, f"backup_{self.current_card_code}_{timestamp}.json")
            try:
                with open(backup_file, "w", encoding="utf-8") as f:
                    json.dump(current_doc_data, f, ensure_ascii=False, indent=4)
                # Ключевой лог
                print(f"Резервная копия успешно создана: {backup_file}")
            except Exception as e:
                print(f"Не удалось создать файл резервной копии: {e}")
                return

            # Очистка таблицы документа: установка N6=0
            print("Очистка таблицы документа: установка N6 = 0...")
            clear_payload = [{"TAB": 0, "LINE": 0, "NAME": "N6", "VALUE": "0"}]
            ok_clear = await update_document(session, self.current_card_code, clear_payload)
            if not ok_clear:
                print("Не удалось очистить таблицу документа. Обновление отменено.")
                return
            # Даём небольшую паузу для надёжности
            await asyncio.sleep(0.25)

            # Формируем тело для перезаписи
            print("Формирование полного тела запроса для перезаписи...")
            request_body: List[Dict[str, Any]] = []

            # Шапка: HNUM/HDAT (совместимость) + N4/N5 + N6
            header_fields = [
                {"NAME": "HNUM", "VALUE": str(doc_num or "")},
                {"NAME": "HDAT", "VALUE": str(doc_date or "")},
                {"NAME": "N4", "VALUE": str(doc_date or "")},  # N4 = дата
                {"NAME": "N5", "VALUE": str(doc_num or "")},   # N5 = номер
                {"NAME": "N6", "VALUE": str(len(self.db_data or []))},
            ]
            # Добавляем F11-поля
            for k, v in SOURCE_HEADER_DATA.items():
                header_fields.append({"NAME": k, "VALUE": str(v)})

            for f in header_fields:
                f["TAB"] = 0
                f["LINE"] = 0
                request_body.append(f)

            # Таблица: используем LINE 0-based, TAB1_A1 (печатный номер) 1-based
            for idx, invoice in enumerate(self.db_data or []):
                line_idx = idx
                display_no = idx + 1
                sum_with_vat = float(invoice.get("r4100g11") or 0)
                vat_sum = float(invoice.get("ndssm") or 0)
                sum_without_vat = sum_with_vat - vat_sum
                nmr_value = invoice.get("nmr")
                nmr_as_string = str(int(nmr_value)) if (nmr_value is not None and str(nmr_value).strip() != "") else ""
                crtdate_val = invoice.get("crtdate")
                crtdate_str = crtdate_val.strftime("%d.%m.%Y") if crtdate_val else ""

                request_body.extend([
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A1", "VALUE": str(display_no)},
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A2", "VALUE": crtdate_str},
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A31", "VALUE": nmr_as_string},
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A4", "VALUE": ""},
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A5", "VALUE": str(invoice.get("docrnn", "") or "")},
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A6", "VALUE": str(invoice.get("pkpinn", "") or "")},
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A7", "VALUE": invoice.get("hnamebuy", "") or ""},
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A8", "VALUE": f"{sum_without_vat:.2f}"},
                    {"TAB": 1, "LINE": line_idx, "NAME": "TAB1_A9", "VALUE": f"{vat_sum:.2f}"},
                ])

            # Отправляем обновлённый пакет
            success = await update_document(session, self.current_card_code, request_body)
            if not success:
                print("Обновление документа завершилось с ошибкой.")
            else:
                # Успех уже залогирован в update_document
                pass

    # -----------------------
    # Restore (восстановление из бэкапа)
    # -----------------------
    def start_restore_thread(self):
        card_code_str = self.card_code_entry.get().strip()
        if not card_code_str.isdigit():
            messagebox.showerror("Ошибка", "Для восстановления укажите CardCode в поле ввода.")
            return

        file_path = filedialog.askopenfilename(title="Выберите файл резервной копии", filetypes=[("JSON files", "*.json")], initialdir="backups")
        if not file_path:
            return

        if messagebox.askyesno("Подтверждение", f"Вы уверены, что хотите восстановить документ с CardCode {card_code_str} из файла:\n\n{os.path.basename(file_path)}?\n\nВсе текущие данные в этом документе будут перезаписаны."):
            self.set_ui_state(is_busy=True)
            threading.Thread(target=self.run_restore_task, args=(int(card_code_str), file_path), daemon=True).start()

    def run_restore_task(self, card_code, file_path):
        original_stdout = sys.stdout
        sys.stdout = QueueWriter(self.log_queue)
        try:
            if not os.path.exists(file_path):
                print(f"Файл резервной копии не найден: {file_path}")
                self.log_queue.put(("TASK_FAILED", None))
                return

            with open(file_path, "r", encoding="utf-8") as f:
                backup_data = json.load(f)
            print(f"Данные из файла '{os.path.basename(file_path)}' успешно прочитаны.")
            asyncio.run(self.async_restore_logic(card_code, backup_data))
            self.log_queue.put(("TASK_COMPLETE", None))
        except Exception as e:
            print(f"КРИТИЧЕСКАЯ ОШИБКА при восстановлении: {e}")
            self.log_queue.put(("TASK_FAILED", None))
        finally:
            sys.stdout = original_stdout

    async def async_restore_logic(self, card_code, backup_data):
        async with aiohttp.ClientSession() as session:
            await update_document(session, card_code, backup_data)

    # -----------------------
    # Queue processing for logs and UI updates
    # -----------------------
    def process_queue(self):
        try:
            while True:
                message = self.log_queue.get_nowait()
                if isinstance(message, tuple):
                    msg_type, data = message
                    if msg_type == "FETCH_COMPLETE":
                        self.set_ui_state(is_busy=False)
                        self.db_data = data
                        if self.db_data is not None:
                            self.populate_treeview_from_db(self.tree_new, self.db_data)
                            self.update_button.config(state=tk.NORMAL)
                    elif msg_type in ["TASK_COMPLETE", "TASK_FAILED"]:
                        self.set_ui_state(is_busy=False)
                        if self.db_data is not None:
                            self.update_button.config(state=tk.NORMAL)
                else:
                    txt = str(message)
                    if not txt.endswith("\n"):
                        txt = txt + "\n"
                    self.log_text.configure(state="normal")
                    self.log_text.insert(tk.END, txt)
                    self.log_text.see(tk.END)
                    self.log_text.configure(state="disabled")
        except queue.Empty:
            pass
        self.root.after(100, self.process_queue)

    # -----------------------
    # Fill preview table
    # -----------------------
    def populate_treeview_from_db(self, tree, data: List[Dict[str, Any]]):
        tree.delete(*tree.get_children())
        for item in data:
            sum_with_vat = float(item.get("r4100g11") or 0)
            vat_sum = float(item.get("ndssm") or 0)
            sum_without_vat = sum_with_vat - vat_sum
            tree.insert("", tk.END, values=(
                item.get("row_no", ""),
                item["crtdate"].strftime("%d.%m.%Y") if item.get("crtdate") else "",
                item.get("nmr", ""), item.get("docrnn", ""), item.get("hnamebuy", ""),
                f"{sum_without_vat:.2f}", f"{vat_sum:.2f}"
            ))


# ========== Запуск приложения ==========
if __name__ == "__main__":
    try:
        from tkcalendar import DateEntry  # проверяем доступность
    except ImportError:
        print("Необходима библиотека tkcalendar. Установите командой: pip install tkcalendar")
        sys.exit(1)

    root = tk.Tk()
    app = App(root)
    root.mainloop()
