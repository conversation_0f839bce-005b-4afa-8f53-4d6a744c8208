# -*- coding: utf-8 -*-

"""
Этот скрипт предназначен для извлечения текста из PDF-файлов с помощью библиотеки PyMuPDF (fitz).
Он открывает PDF-документ, проходит по всем страницам и извлекает текстовое содержимое.

Основные функции:
- extract_text_from_pdf(pdf_path): Извлекает весь текст из указанного PDF-файла.

Использование:
- Укажите путь к PDF-файлу в переменной pdf_path.
- Запустите скрипт для извлечения и вывода текста.

Зависимости:
- PyMuPDF (pip install PyMuPDF)
"""

import fitz  # PyMuPDF

def extract_text_from_pdf(pdf_path):
    text = ""
    with fitz.open(pdf_path) as doc:
        for page in doc:
            text += page.get_text()
    return text


if __name__ == '__main__':
    # scan
    pdf_path = r"c:\Users\<USER>\Desktop\Разблокировка\32490244_ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ ЕПІЦЕНТР К\202409\Видаткова накладна\Видаткова накладна №10663 від 14 09 2024.pdf"     
    
    # EDI text
    # pdf_path = r"c:\Users\<USER>\Desktop\Разблокировка\32490244_ТОВАРИСТВО З ОБМЕЖЕНОЮ ВІДПОВІДАЛЬНІСТЮ ЕПІЦЕНТР К\202409\Видаткова накладна\Видаткова накладна №10662 від 14 09 2024.pdf" 
    
    # Medoc 3 печати на 1 стр
    # pdf_path = r"C:\Rasim\Python\Medoc\31316718\202411\ПН\ПН 122320 20 11 2024.pdf"
    extracted_text = extract_text_from_pdf(pdf_path)
    print(extracted_text)
