# -*- coding: utf-8 -*-
# Выгрузка из Medoc документов клиента в PDF за указанный период
# downloadPdf_GUI.py

import asyncio
import base64
import json
import os
import re
import logging
import shutil
import sys
import threading
import queue
from datetime import datetime, date
from typing import Optional, List, Dict, Any, Tuple
from collections import defaultdict
from dotenv import load_dotenv
import aiohttp
from dateutil.relativedelta import relativedelta
from dateutil.parser import parse
import fitz  # PyMuPDF
import openpyxl

# --- Импорты для GUI ---
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext

load_dotenv()

# --- Настройка логирования для вывода в GUI ---
logger = logging.getLogger()
logger.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

for handler in logger.handlers[:]:
    logger.removeHandler(handler)

console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

# --- Конфигурация ---
HOSTNAME_PUBLIC = os.getenv("PG_HOST_LOCAL", "*************")
ID_ORG = 781

#
# --- ВАШ РАБОЧИЙ КОД (БЕЗ ИЗМЕНЕНИЙ) ---
#
def clean_filename(filename: str) -> str:
    return re.sub(r'[\\/*?:"<>|]', "", filename).strip()

def doc_type_short(doc_type: str) -> str:
    doc_type = doc_type.lower()
    if doc_type == 'податкова накладна':
        return 'ПН'
    if doc_type in ['дoдаток','додаток']:  # выглядят одинаково, но разные буквы о
        return 'РК'
    return doc_type.upper()

def get_doc_type_name(docname: Optional[str]) -> str:
    if not docname:
        return "_Інше"
    separator_index = docname.find('№')
    if separator_index == -1:
        return doc_type_short(docname.strip())
    return doc_type_short(docname[:separator_index].strip())

def split_date_range_by_month(start_date_str: str, end_date_str: str) -> List[Tuple[date, date]]:
    date_format = '%Y/%m/%d'
    start_dt = datetime.strptime(start_date_str, date_format).date()
    end_dt = datetime.strptime(end_date_str, date_format).date()
    if start_dt + relativedelta(months=1) > end_dt:
        return [(start_dt, end_dt)]
    logging.info("Диапазон дат слишком большой. Разбиваю на месячные интервалы...")
    date_ranges = []
    current_start = start_dt
    while current_start <= end_dt:
        chunk_end = current_start + relativedelta(months=1) - relativedelta(days=1)
        if chunk_end > end_dt:
            chunk_end = end_dt
        date_ranges.append((current_start, chunk_end))
        current_start = chunk_end + relativedelta(days=1)
    return date_ranges

async def fetch_one_url(session: aiohttp.ClientSession, url: str, semaphore: asyncio.Semaphore) -> Optional[Any]:
    async with semaphore:
        try:
            headers = {"Connection": "close"}
            timeout = aiohttp.ClientTimeout(total=60)
            async with session.get(url, headers=headers, timeout=timeout) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    logging.error(f"Ошибка запроса к {url}. Статус: {response.status}, Ответ: {await response.text()}")
                    return None
        except Exception as e:
            logging.error(f"Непредвиденная ошибка при запросе к {url}: {e}")
            return None

def is_pdf_defective(file_path: str, letter_ratio_threshold: float = 0.4) -> bool:
    try:
        doc = fitz.open(file_path)
        full_text = "".join(page.get_text() for page in doc)
        doc.close()
        clean_text = "".join(full_text.split())
        if not clean_text: return False
        letter_chars = sum(1 for char in clean_text if char.isalpha())
        letter_ratio = letter_chars / len(clean_text)
        logging.info(f"Анализ файла '{os.path.basename(file_path)}': Доля букв: {letter_ratio:.2f}")
        return letter_ratio < letter_ratio_threshold
    except Exception as e:
        logging.error(f"Критическая ошибка при обработке PDF '{file_path}': {e}")
        return True

async def get_document_as_pdf(session: aiohttp.ClientSession, doc: Dict[str, Any], semaphore: asyncio.Semaphore,
                              facsimile: bool, base_save_path: str, suffix: str = "") -> Optional[Tuple[str, str]]:
    doc_id = doc.get('doc_id')
    url = f"http://{HOSTNAME_PUBLIC}:63777/api/Info/PrintDocPDF?idOrg={ID_ORG}&docID={doc_id}&facsimile={str(facsimile).lower()}"
    data = await fetch_one_url(session, url, semaphore)
    if not data:
        logging.warning(f"Нет ответа от API для doc_id: {doc_id} (facsimile={facsimile}).")
        return None
    try:
        if not isinstance(data, list) or len(data) == 0:
            logging.error(f"Неожиданный или пустой формат ответа API для doc_id: {doc_id}")
            return None
        document_info = data[0]
        if not isinstance(document_info, dict):
            logging.error(f"Первый элемент не является словарем для doc_id: {doc_id}")
            return None

        file_raw = document_info.get('File')
        file_name_from_api = document_info.get('FileName')
        doc_type = get_doc_type_name(file_name_from_api)
        
        if not file_raw:
            logging.error(f"В ответе API отсутствует 'File' для doc_id: {doc_id}")
            return None

        base_name = ""
        period = ''
        if file_name_from_api:
            doc_num = doc.get('doc_num', '')
            doc_date_str = doc.get('doc_date')
            formatted_date = ''
            if doc_date_str:
                try:
                    parsed_date = parse(doc_date_str, dayfirst=True)
                    formatted_date = parsed_date.strftime('%d %m %Y')
                    period = parsed_date.strftime('%Y%m')
                except (ValueError, TypeError): pass
                base_name = f"{doc_type} {doc_num} {formatted_date}"

        if not base_name:
            logging.error(f"Не удалось сформировать базовое имя для doc_id: {doc_id}. Используется запасное имя.")
            base_name = f"UNNAMED_{doc_id}"

        final_name = base_name.replace('.', ' ').upper()
        final_file_name = f"{clean_filename(final_name)}{suffix}.pdf"

        good_save_dir = os.path.join(base_save_path, period, doc_type)
        os.makedirs(good_save_dir, exist_ok=True)
        file_path = os.path.join(good_save_dir, final_file_name)

        file_data = base64.b64decode(file_raw)
        with open(file_path, 'wb') as f:
            f.write(file_data)

        if is_pdf_defective(file_path):
            bad_dir = os.path.join(base_save_path, 'Bad')
            os.makedirs(bad_dir, exist_ok=True)
            new_pdf_path = os.path.join(bad_dir, final_file_name)
            json_path = new_pdf_path.replace('.pdf', '.json')
            try:
                shutil.move(file_path, new_pdf_path)
                logging.warning(f"Обнаружен дефектный PDF: '{final_file_name}'. Файл перемещен в '{bad_dir}'")
            except OSError as e:
                logging.error(f"Не удалось переместить файл {file_path} в {new_pdf_path}: {e}")
                json_path = file_path.replace('.pdf', '.json')
            with open(json_path, 'w', encoding='utf-8') as f_json:
                json.dump(data, f_json, ensure_ascii=False, indent=4)

        return file_path, doc_type
    except Exception as e:
        logging.error(f"Произошла непредвиденная ошибка при обработке doc_id {doc_id}: {e}")
        return None

async def download_documents_for_partner(session: aiohttp.ClientSession, partner_edrpou: str, date_from: str,
                                         date_end: str, semaphore: asyncio.Semaphore, base_save_path: str):
    logging.info(f"Запуск процесса загрузки для партнёра {partner_edrpou} с {date_from} по {date_end}")
    logging.info(f"Файлы будут сохранены в папку: {base_save_path}")

    date_ranges = split_date_range_by_month(date_from, date_end)
    all_documents = []
    for start_chunk, end_chunk in date_ranges:
        chunk_from_str, chunk_end_str = start_chunk.strftime('%Y/%m/%d'), end_chunk.strftime('%Y/%m/%d')
        logging.info(f"Запрос списка документов за период: {chunk_from_str} - {chunk_end_str}")
        url = (f"http://{HOSTNAME_PUBLIC}:63777/api/Info/GetPrimaryReestr?"
               f"idOrg={ID_ORG}&docType=-1&moveType=0&dateFrom={chunk_from_str}&dateEnd={chunk_end_str}")
        documents_chunk = await fetch_one_url(session, url, semaphore)
        if documents_chunk:
            all_documents.extend(documents_chunk)
        else:
            logging.warning(f"Не удалось получить документы за период {chunk_from_str} - {chunk_end_str}.")
        await asyncio.sleep(1)

    if not all_documents:
        logging.warning("Не удалось получить данные о документах ни за один из периодов.")
        return

    partner_docs = [doc for doc in all_documents if doc.get('partner_edrpou') == partner_edrpou]
    if not partner_docs:
        logging.info(f"Для партнёра {partner_edrpou} не найдены документы в указанном диапазоне дат.")
        return

    grouped_by_id = defaultdict(list)
    for doc in partner_docs:
        if doc.get('doc_id'):
            grouped_by_id[doc['doc_id']].append(doc)

    unique_partner_docs = []
    for doc_id, doc_group in grouped_by_id.items():
        if len(doc_group) == 1:
            unique_partner_docs.append(doc_group[0])
        else:
            try:
                sorted_group = sorted(doc_group, key=lambda d: datetime.fromisoformat(d.get('moddate', '1970-01-01T00:00:00')), reverse=True)
                unique_partner_docs.append(sorted_group[0])
            except (ValueError, TypeError) as e:
                logging.warning(f"Ошибка сортировки дубликатов для doc_id {doc_id}: {e}. Будет использован первый.")
                unique_partner_docs.append(doc_group[0])

    logging.info(f"Всего найдено в реестре (с дубликатами): {len(partner_docs)} документов.")
    logging.info(f"Найдено уникальных документов (с учетом 'moddate'): {len(unique_partner_docs)}. Начинаю загрузку PDF...")

    tasks = [get_document_as_pdf(session, doc, semaphore, True, base_save_path) for doc in unique_partner_docs]
    results = await asyncio.gather(*tasks)

    failed_docs, successful_count, excel_data = [], 0, []
    for doc, result in zip(unique_partner_docs, results):
        if result:
            _, doc_type = result
            successful_count += 1
            doc_date_str = doc.get('doc_date')
            formatted_doc_date = parse(doc_date_str).strftime('%d.%m.%Y') if doc_date_str else ''
            excel_data.append([doc_type, formatted_doc_date, doc.get('doc_num', '')])
        else:
            failed_docs.append(doc)

    total_files_on_disk = sum(len(files) for _, _, files in os.walk(base_save_path)) if os.path.exists(base_save_path) else 0

    logging.info("\n" + "=" * 40)
    logging.info("--- ИТОГИ ЗАГРУЗКИ ---")
    logging.info(f"Найдено в реестре (с дубликатами): {len(partner_docs)}")
    logging.info(f"Найдено уникальных документов: {len(unique_partner_docs)}")
    logging.info(f"✅ Успешно загружено по данным скрипта: {successful_count}")
    logging.info(f"💽 Фактически файлов в папках: {total_files_on_disk}")

    if failed_docs:
        failed_ids = [d.get('doc_id', 'N/A') for d in failed_docs]
        logging.error(f"❌ Не удалось загрузить: {len(failed_docs)} файлов. ID: {failed_ids}")
    logging.info("=" * 40 + "\n")

    if excel_data:
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            excel_filename = f"{partner_edrpou}_{timestamp}.xlsx"
            excel_filepath = os.path.join(base_save_path, excel_filename)
            
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "Список документов"
            ws.append(['Тип документа', 'Дата', 'Номер'])
            for row_data in excel_data:
                ws.append(row_data)
            
            for col in ws.columns:
                max_length = max(len(str(cell.value)) for cell in col if cell.value)
                ws.column_dimensions[col[0].column_letter].width = max_length + 2
            
            wb.save(excel_filepath)
            logging.info(f"Данные о документах сохранены в Excel файл: {excel_filepath}")
        except Exception as e:
            logging.error(f"Не удалось сохранить Excel файл. Ошибка: {e}")

    logging.info("🎉 Все задачи по загрузке завершены.")

#
# --- КОД ДЛЯ GUI (С ИЗМЕНЕНИЯМИ) ---
#

class QueueHandler(logging.Handler):
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue

    def emit(self, record):
        self.log_queue.put(self.format(record))

class PdfDownloaderApp:
    def __init__(self, master):
        self.master = master
        master.title("Загрузчик документов из Medoc")
        master.geometry("800x600")

        self.log_queue = queue.Queue()
        self.queue_handler = QueueHandler(self.log_queue)
        logger.addHandler(self.queue_handler)

        self.create_widgets()
        self.master.after(100, self.process_log_queue)

    ### НОВОЕ: Функция для создания поля ввода с контекстным меню ###
    def _create_entry_with_context_menu(self, parent, textvariable):
        entry = ttk.Entry(parent, textvariable=textvariable)
        
        # Создание контекстного меню
        context_menu = tk.Menu(entry, tearoff=0)
        context_menu.add_command(label="Вырезать", command=lambda: entry.event_generate("<<Cut>>"))
        context_menu.add_command(label="Копировать", command=lambda: entry.event_generate("<<Copy>>"))
        context_menu.add_command(label="Вставить", command=lambda: entry.event_generate("<<Paste>>"))
        
        # Привязка меню к правому клику
        entry.bind("<Button-3>", lambda event: context_menu.tk_popup(event.x_root, event.y_root))
        
        # Привязка горячих клавиш (для macOS и Windows/Linux)
        # Это может быть избыточно, если система делает это сама, но гарантирует работу
        entry.bind_class("TEntry", "<Control-a>", lambda event: event.widget.select_range(0, 'end'))
        entry.bind_class("TEntry", "<Command-a>", lambda event: event.widget.select_range(0, 'end'))
        
        return entry

    def create_widgets(self):
        main_frame = ttk.Frame(self.master, padding="10")
        main_frame.pack(fill="both", expand=True)

        input_frame = ttk.LabelFrame(main_frame, text="Параметры")
        input_frame.pack(fill="x", expand=False)
        input_frame.columnconfigure(1, weight=1)

        # Код клиента
        ttk.Label(input_frame, text="Код клиента (ЄДРПОУ):").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.partner_var = tk.StringVar(value="05475067")
        ### ИЗМЕНЕНО: Используем новую функцию для создания поля ###
        self.partner_entry = self._create_entry_with_context_menu(input_frame, self.partner_var)
        self.partner_entry.grid(row=0, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        # Даты
        ttk.Label(input_frame, text="Дата начала (ГГГГ/ММ/ДД):").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.date_from_var = tk.StringVar(value="2025/06/01")
        ### ИЗМЕНЕНО: Используем новую функцию для создания поля ###
        self.date_from_entry = self._create_entry_with_context_menu(input_frame, self.date_from_var)
        self.date_from_entry.grid(row=1, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        ttk.Label(input_frame, text="Дата конца (ГГГГ/ММ/ДД):").grid(row=2, column=0, padx=5, pady=5, sticky="w")
        self.date_to_var = tk.StringVar(value=datetime.today().strftime('%Y/%m/%d'))
        ### ИЗМЕНЕНО: Используем новую функцию для создания поля ###
        self.date_to_entry = self._create_entry_with_context_menu(input_frame, self.date_to_var)
        self.date_to_entry.grid(row=2, column=1, columnspan=2, padx=5, pady=5, sticky="ew")

        # Папка сохранения
        ttk.Label(input_frame, text="Папка для сохранения:").grid(row=3, column=0, padx=5, pady=5, sticky="w")
        self.save_dir_var = tk.StringVar(value=os.getcwd())
        self.save_dir_entry = ttk.Entry(input_frame, textvariable=self.save_dir_var, state="readonly")
        self.save_dir_entry.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        self.browse_button = ttk.Button(input_frame, text="Выбрать...", command=self.browse_directory)
        self.browse_button.grid(row=3, column=2, padx=5, pady=5, sticky="e")

        self.run_button = ttk.Button(main_frame, text="Начать загрузку", command=self.start_download_thread)
        self.run_button.pack(pady=10)

        log_frame = ttk.LabelFrame(main_frame, text="Лог выполнения")
        log_frame.pack(fill="both", expand=True)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, state='disabled')
        self.log_text.pack(fill="both", expand=True, padx=5, pady=5)

    def browse_directory(self):
        directory = filedialog.askdirectory(initialdir=self.save_dir_var.get())
        if directory:
            self.save_dir_var.set(directory)

    def process_log_queue(self):
        while not self.log_queue.empty():
            message = self.log_queue.get()
            self.log_text.configure(state='normal')
            self.log_text.insert(tk.END, message + '\n')
            self.log_text.yview(tk.END)
            self.log_text.configure(state='disabled')
        self.master.after(100, self.process_log_queue)

    def set_controls_state(self, state):
        for widget in [self.partner_entry, self.date_from_entry, self.date_to_entry, self.browse_button, self.run_button]:
            widget.config(state=state)

    def start_download_thread(self):
        partner_edrpou = self.partner_var.get().strip()
        date_from = self.date_from_var.get().strip()
        date_to = self.date_to_var.get().strip()
        save_dir = self.save_dir_var.get()

        if not all([partner_edrpou, date_from, date_to, save_dir]):
            messagebox.showerror("Ошибка", "Все поля должны быть заполнены.")
            return
        try:
            datetime.strptime(date_from, '%Y/%m/%d')
            datetime.strptime(date_to, '%Y/%m/%d')
        except ValueError:
            messagebox.showerror("Ошибка", "Неверный формат даты. Используйте ГГГГ/ММ/ДД.")
            return
        
        base_save_path = os.path.join(save_dir, clean_filename(partner_edrpou))
        os.makedirs(base_save_path, exist_ok=True)

        self.set_controls_state("disabled")
        self.run_button.config(text="Загрузка...")

        threading.Thread(
            target=self.run_async_task,
            args=(partner_edrpou, date_from, date_to, base_save_path),
            daemon=True
        ).start()

    def run_async_task(self, partner_edrpou, date_from, date_to, base_save_path):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            semaphore = asyncio.Semaphore(1)
            async def task_wrapper():
                async with aiohttp.ClientSession() as session:
                    await download_documents_for_partner(
                        session, partner_edrpou, date_from, date_to, semaphore, base_save_path
                    )
            loop.run_until_complete(task_wrapper())
        except Exception as e:
            logging.error(f"Критическая ошибка в потоке выполнения: {e}")
        finally:
            loop.close()
            self.master.after(0, self.on_download_complete)

    def on_download_complete(self):
        self.set_controls_state("normal")
        self.run_button.config(text="Начать загрузку")
        messagebox.showinfo("Завершено", "Процесс загрузки завершен. Проверьте лог для деталей.")


if __name__ == '__main__':
    root = tk.Tk()
    app = PdfDownloaderApp(root)
    root.mainloop()