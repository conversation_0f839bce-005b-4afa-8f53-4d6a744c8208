# mainMedoc.py - Синхронная версия с многопоточностью
import asyncio
import time
from datetime import date, datetime
from dateutil.relativedelta import relativedelta
from dateutil import parser
import xmltodict
import logging
from typing import Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import os
from AsyncHTTPMultiThreade import fetch_one_url, check_server_availability, wait_for_server_availability
from DeleteDoubleRowsFromTableMultiThreade import delete_double_rows_from_table
from GoogleSheetsPostgresql import google_sheets_update_register
from MedocConfig import doc_type_name
from SaveDocToPdf import create_table, main_medoc_save_doc_to_pdf
from Views.vwReestrAndDocInfo import vw_reestr_and_docinfo_sync
from tGetDocInfo import save_doc_info_sync
from tRegister import save_register_sync
from dotenv import load_dotenv


# Загрузка переменных окружения из .env файла
load_dotenv()
hostname_public = os.getenv('MEDOC_API_HOST')

# Настройка логирования
logger = logging.getLogger(__name__)

doc_type = doc_type_name['Всі']  # 10100

# Конфигурация повторных попыток
MAX_RETRIES = 3
RETRY_DELAY = 5
SEMAPHORE_LIMIT = 5  # Ограничение одновременных операций


# Отримання квитанцій документа
def get_url_checks(cardcode):
    return f"http://{hostname_public}:63777/api/Info/GetDocKVT?cardCode={cardcode}"


# Пошук і отримання ідентифікатора установи за заданими ЄДРПОУ установи та номером філії
def get_idorg(my_okpo) -> Optional[int]:
    """Получение ID организации с повторными попытками."""
    url = f"http://{hostname_public}:63777/api/System/GetOrgCode?edrpou={my_okpo}"
    
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            response = fetch_one_url(url)
            return response
        except Exception as e:
            logger.error(f"Error getting idorg (attempt {attempt}): {e}")
            if attempt < MAX_RETRIES:
                time.sleep(RETRY_DELAY * attempt)
            else:
                return None


def get_counterparty_name(xml_data):
    """Извлечение имени контрагента из XML с обработкой ошибок."""
    result = None
    try:
        if xml_data:
            xml_dict = xmltodict.parse(xml_data)
            result = xml_dict['DECLAR']['DECLARBODY']['HNAMEBUY']
    except Exception as e:
        logger.error(f"Error parsing counterparty name from XML: {e}")
    finally:
        return result


def get_doc_status(xml_data):
    """Извлечение статуса документа из XML с обработкой ошибок."""
    result = None
    try:
        if xml_data:
            xml_dict = xmltodict.parse(xml_data)
            result = xml_dict['DECLAR']['DECLARBODY']['HRESULT']
    except Exception as e:
        logger.error(f"Error parsing doc status from XML: {e}")
    finally:
        return result


def get_url(**kwargs):
    idorg = kwargs['idorg']
    datefrom = kwargs['datefrom']
    dateend = kwargs['dateend']
    return (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg={idorg}"
            f"&docType={doc_type}&moveType=0&dateFrom={datefrom}&dateEnd={dateend}")


def get_date_from(control_date):
    """Преобразование даты с улучшенной обработкой ошибок."""
    try:
        if isinstance(control_date, datetime):
            return control_date.date()
        elif isinstance(control_date, date):
            return control_date
        else:
            return parser.parse(control_date).date()
    except Exception as e:
        logger.error(f"Error parsing date {control_date}: {e}")
        # Возвращаем текущую дату минус месяц как fallback
        return datetime.today().date() - relativedelta(months=1)


def process_date_range(date_first: date, date_last: date) -> bool:
    """Обработка одного диапазона дат с повторными попытками."""
    date_one = date_first.strftime("%Y/%m/%d")
    date_two = date_last.strftime("%Y/%m/%d")
    
    logger.info(f"Processing date range: {date_one} - {date_two}")
    
    # Дополнительная проверка доступности перед критичной операцией
    if not check_server_availability():
        logger.warning(f"Сервер недоступен для периода {date_one} - {date_two}")
        return False
    
    url = (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg=781"
           f"&docType={doc_type}&moveType=0&dateFrom={date_one}&dateEnd={date_two}")
    
    try:
        df = save_register_sync(url)
        if not df.empty:  # может быть None, если нет данных в периоде
            save_doc_info_sync(df)
            return True
        else:
            logger.info(f"No data for period {date_one} - {date_two}")
            return False
    except Exception as e:
        logger.error(f"Error processing date range {date_one} - {date_two}: {e}")
        return False


def main_medoc(date_first: datetime.date):
    """Основная функция с улучшенной обработкой ошибок."""
    try:
        # Проверяем доступность сервера в самом начале
        logger.info(f"Проверка доступности сервера {hostname_public}:63777...")
        if not check_server_availability():
            logger.warning(f"Сервер {hostname_public}:63777 недоступен. Ожидание...")
            # Ждем доступности сервера до 5 минут (10 попыток по 30 секунд)
            if not wait_for_server_availability(max_attempts=10, delay=30):
                logger.critical(f"КРИТИЧЕСКАЯ ОШИБКА: Сервер {hostname_public}:63777 недоступен после всех попыток!")
                logger.critical("Проверьте:")
                logger.critical("1. Запущен ли сервер Medoc")
                logger.critical("2. Правильный ли адрес в переменной окружения PG_HOST")
                logger.critical("3. Доступен ли порт 63777")
                logger.critical("4. Нет ли блокировки файрволом")
                raise Exception("Сервер недоступен")
        
        logger.info(f"Сервер {hostname_public}:63777 доступен")
        
        # Создаем таблицы
        asyncio.run(create_table())
        
        # Опционально очищаем таблицы (закомментировано в оригинале)
        # clear_table_sync('medoc_reestr')
        # clear_table_sync('medoc_doc_info')
        
        date_first = get_date_from(date_first)
        current_date = date_first
        
        # Счетчики для статистики
        total_ranges = 0
        successful_ranges = 0
        
        # Собираем все диапазоны дат для обработки
        date_ranges = []
        while current_date <= datetime.today().date():
            date_last = current_date + relativedelta(weeks=1)
            date_ranges.append((current_date, date_last))
            current_date = date_last + relativedelta(days=1)
        
        total_ranges = len(date_ranges)
        logger.info(f"Будет обработано {total_ranges} диапазонов дат")
        
        # Используем многопоточность для обработки диапазонов
        with ThreadPoolExecutor(max_workers=SEMAPHORE_LIMIT) as executor:
            # Создаем задачи для каждого диапазона
            future_to_range = {}
            for date_range in date_ranges:
                # Обрабатываем диапазон с повторными попытками
                def process_with_retries(date_range):
                    date_first, date_last = date_range
                    for attempt in range(1, MAX_RETRIES + 1):
                        try:
                            return process_date_range(date_first, date_last)
                        except Exception as e:
                            logger.error(f"Error processing range (attempt {attempt}): {e}")
                            if attempt < MAX_RETRIES:
                                time.sleep(RETRY_DELAY * attempt)
                    return False
                
                future = executor.submit(process_with_retries, date_range)
                future_to_range[future] = date_range
            
            # Обрабатываем результаты по мере готовности
            for future in as_completed(future_to_range):
                date_range = future_to_range[future]
                try:
                    success = future.result()
                    if success:
                        successful_ranges += 1
                    else:
                        logger.warning(f"Failed to process range {date_range[0]} - {date_range[1]}")
                except Exception as e:
                    logger.error(f"Exception for range {date_range[0]} - {date_range[1]}: {e}")
        
        logger.info(f"Date ranges processed: {successful_ranges}/{total_ranges} successful")
        
        # Создаем представление
        vw_reestr_and_docinfo_sync()
        
        # Удаляем дубликаты
        delete_double_rows_from_table('medoc_reestr')
        delete_double_rows_from_table('medoc_doc_info')
        
        # Отправляем менеджерам на почту счета в PDF
        asyncio.run(main_medoc_save_doc_to_pdf())
        
        # Обновляем таблицу в Google Sheets (закладка Medoc)
        google_sheets_update_register()
        
        logger.info("Medoc processing completed")
        
    except Exception as e:
        logger.critical(f"Critical error in main_medoc: {e}")
        raise
 

if __name__ == '__main__':
    # Настройка логирования
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('medoc_main.log'),
            logging.StreamHandler()
        ]
    )
    
    start = datetime.now()
    print(f"Start: {start}")
    
    date_first = datetime.today().date() - relativedelta(months=1)
    
    try:
        main_medoc(date_first)
    except Exception as e:
        logger.critical(f"Failed to run main_medoc: {e}")
    
    end = datetime.now()
    print(f"End: {end}, Duration: {end - start}")