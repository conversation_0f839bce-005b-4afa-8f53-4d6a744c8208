import asyncio
import json
import time
import os
from datetime import datetime
from itertools import islice
from dotenv import load_dotenv
import aiohttp
from dateutil.relativedelta import relativedelta
import requests


load_dotenv()
hostname_public = os.getenv('MEDOC_API_HOST')


async def check_server_availability():
    """Проверка доступности сервера перед началом работы."""
    try:
        timeout = aiohttp.ClientTimeout(total=5)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(f"http://{hostname_public}:63777") as response:
                return response.status == 200
    except Exception as e:
        print(f"Сервер недоступен: {e}")
        return False

async def wait_for_server_availability(max_attempts=10, delay=30):
    """
    Ожидание доступности сервера с периодическими проверками.

    Args:
        max_attempts: Максимальное количество попыток
        delay: Задержка между попытками в секундах

    Returns:
        bool: True если сервер стал доступен, False если превышено количество попыток
    """
    for attempt in range(1, max_attempts + 1):
        print(f"Попытка {attempt}/{max_attempts} подключения к серверу...")

        if await check_server_availability():
            print(f"Сервер {hostname_public}:63777 доступен!")
            return True

        if attempt < max_attempts:
            print(f"Сервер недоступен. Ожидание {delay} секунд перед следующей попыткой...")
            await asyncio.sleep(delay)

    print(f"Сервер {hostname_public}:63777 недоступен после {max_attempts} попыток")
    return False


def get_data_sync_from_url(url):
    """Синхронное получение данных - fallback метод."""
    try:
        # Увеличиваем таймаут до 60 секунд
        response = requests.get(url, timeout=60)
        if response.status_code == 200:
            result = response.text
            if result:  # Проверка на пустой ответ
                return json.loads(result)
    except Exception as e:
        print(f"Ошибка при получении данных из url: {url} - {e}")
    return None


async def fetch_url(session, url, count=1):
    """Асинхронное получение данных с повторными попытками."""
    while count < 5:
        try:
            async with session.get(url) as response:
                if response.status == 200:
                    result = await response.text()
                    if result:  # Проверка на пустой ответ
                        return json.loads(result)
                print(f"Получен статус {response.status} для {url}")
        except asyncio.TimeoutError:
            print(f"Попытка {count} - таймаут для {url}")
        except Exception as e:
            # Логируем конкретную ошибку для отладки
            print(f"Попытка {count} не удалась для {url}: {type(e).__name__}: {e}")

        # ВАЖНО: используем await asyncio.sleep вместо time.sleep
        sleep_time = min(5 * count, 30)  # Максимум 30 секунд между попытками
        print(f"Ожидание {sleep_time} секунд перед попыткой {count + 1}...")
        await asyncio.sleep(sleep_time)
        count += 1

    # Fallback на синхронный метод
    print(f"Переход на синхронный метод для {url}")
    result = get_data_sync_from_url(url)
    if result:
        return result

    print(f"Не удалось получить данные из url: {url} после 5 попыток")
    return None


async def fetch_one_url(url):
    """Получение данных из одного URL."""
    # Проверяем доступность сервера перед запросом
    if not await check_server_availability():
        print(f"Сервер {hostname_public}:63777 недоступен")
        return None

    # Увеличиваем таймаут до 60 секунд
    timeout = aiohttp.ClientTimeout(total=60)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        response = await fetch_url(session, url)
        return response


# Закомментированная версия сохранена как в оригинале
# async def fetch_all_urls(urls):
#     async with aiohttp.ClientSession() as session:
#         tasks = [fetch_url(session, url) for url in urls]
#         result = await asyncio.gather(*tasks)
#         return result


async def fetch_all_urls(urls):
    """Получение данных из множества URL с ограничением одновременных запросов."""
    # Проверяем доступность сервера перед запросами
    if not await check_server_availability():
        print(f"Сервер {hostname_public}:63777 недоступен")
        return [None] * len(urls)

    # Увеличиваем таймаут до 60 секунд
    timeout = aiohttp.ClientTimeout(total=60)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        # Создаем семафор для ограничения до 5 одновременных запросов
        semaphore = asyncio.Semaphore(5)

        async def fetch_with_semaphore(url):
            async with semaphore:
                return await fetch_url(session, url)

        # Создаем задачи с семафором
        tasks = [fetch_with_semaphore(url) for url in urls]

        # Выполняем все задачи
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Фильтруем результаты от исключений
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                print(f"Ошибка при получении данных: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        return processed_results


# ИСПРАВЛЕНО: правильный синтаксис
if __name__ == '__main__':
    async def main():
        # Сначала проверяем доступность сервера
        if not await check_server_availability():
            print(f"ОШИБКА: Сервер {hostname_public}:63777 недоступен!")
            print("Проверьте:")
            print("1. Запущен ли сервер")
            print("2. Правильный ли адрес в переменной окружения PG_HOST")
            print("3. Доступен ли порт 63777")
            return

        print(f"Сервер {hostname_public}:63777 доступен")

        date_last = datetime.today().date().strftime("%Y/%m/%d")
        date_first = (datetime.today().date() - relativedelta(months=3)).strftime("%Y/%m/%d")
        url = (f"http://{hostname_public}:63777/api/Info/GetPrimaryReestr?idOrg=781"
               f"&docType=-1&moveType=0&dateFrom={date_first}&dateEnd={date_last}")

        result = await fetch_one_url(url)
        if result:
            print(f"Успешно получены данные: {len(str(result))} символов")
        else:
            print("Не удалось получить данные")


    asyncio.run(main())