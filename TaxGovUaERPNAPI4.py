"""
17.08.2023 16:07
скрипт для загрузки данных ЕРПН налоговой (tax.gov.ua) в базу данных.
"""

import asyncio
import os
from datetime import datetime
import aiohttp
from dateutil.parser import parse
from ChromeDriverUpdater import update_chromedriver_if_needed
from dateutil.relativedelta import relativedelta

# Обновляем ChromeDriver, если это необходимо
update_chromedriver_if_needed()

# --- Импорты из ваших модулей ---
try:
    from TaxGovUaConfig import get_token, remove_duplicates
    from AsyncPostgresql import async_save_pg
    from tables.t_tax_cabinet_erpn_api import SQL_INSERT_ERPN
    from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_ADD_FROM_ERPN_API
except ImportError as e:
    print(f"🔥 Ошибка импорта: Не удалось найти необходимый модуль. {e}")
    print("Пожалуйста, убедитесь, что файлы 'TaxGovUaConfig.py', 'AsyncPostgresql.py' и 'tables/t_tax_cabinet_erpn_api.py' находятся в проекте.")
    exit()

# --- КОНФИГУРАЦИЯ ---
URL_API_BASE = "https://cabinet.tax.gov.ua/ws/api"
URL_DOC_LIST = f"{URL_API_BASE}/nlnk/nlnkhd"

PARAMS = {
    "fromImpdate": (datetime.now() - relativedelta(months=40)).replace(day=1).strftime("%Y-%m-%d 00:00:00"),  # Начало периода время указывать обязательно
    "toImpdate": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),  # Конец периода время указывать обязательно
    "size": 1000,  # Максимальное количество записей на странице
}


async def fetch_async(session, url, token, params=None, retries=5):
    """
    Асинхронная функция для выполнения HTTP-запроса с повторными попытками.
    """
    headers = {"Authorization": token, "Content-Type": "application/json"}
    
    for attempt in range(retries):
        try:
            await asyncio.sleep(1) 
            async with session.get(url, headers=headers, params=params, timeout=45) as response:
                response.raise_for_status()
                return await response.json()
        except (aiohttp.ClientError, asyncio.TimeoutError, OSError) as e:
            page_num = params.get('page', 'N/A')
            # print(f"⚠️ Ошибка при запросе страницы {page_num} (попытка {attempt + 1}/{retries}): {e}")
            if attempt < retries - 1:
                delay = 2 ** (attempt + 1)
                # print(f"Повторный запрос через {delay} сек...")
                await asyncio.sleep(delay)
            else:
                print(f"🔥 Не удалось получить данные для страницы {page_num} после {retries} попыток.")
                await main()
                return None
    return None

async def convert_data(data_list):
    """Преобразует типы данных для одной записи перед вставкой в БД."""
    try:
        data_list[3] = int(data_list[3]) if data_list[3] else 0
        data_list[11] = int(data_list[11]) if data_list[11] else 0
        data_list[4] = parse(data_list[4]) if data_list[4] else None
        data_list[13] = parse(data_list[13]) if data_list[13] else None
        data_list[18] = parse(data_list[18]) if data_list[18] else None
        return tuple(data_list)
    except (ValueError, TypeError) as e:
        print(f"⚠️ Ошибка конвертации данных: {e} для записи: {data_list}")
        return None

async def save_page_content_to_db(page_content):
    """
    Конвертирует и сохраняет содержимое одной страницы в базу данных.
    """
    if not page_content:
        print("Нет данных для сохранения.")
        return 0

    print(f"💾 Подготовка к сохранению {len(page_content)} записей в БД...")
    
    values_list = [list(rec.values()) for rec in page_content]
    conversion_tasks = [convert_data(data) for data in values_list]
    converted_data = await asyncio.gather(*conversion_tasks)
    
    final_data = [data for data in converted_data if data is not None]
    
    if not final_data:
        print("⚠️ Нет данных для сохранения после фильтрации и конвертации.")
        return 0

    try:
        await async_save_pg(SQL_INSERT_ERPN, final_data)
        print(f"✅ Успешно сохранено {len(final_data)} записей.")
        return len(final_data)
    except Exception as e:
        print(f"🔥 Критическая ошибка при сохранении в БД: {e}")
        return 0


async def main():
    print("🚀 Запуск скрипта...")
    driver = None
    token = None
    total_saved_count = 0
    total_records_received = 0
    failed_requests = []

    try:
        print("Получение токена аутентификации...")
        driver, token = get_token()
        if not token:
            print("🔥 Критическая ошибка: Не удалось получить токен.")
            return
        print("✅ Токен успешно получен.")
        
        async with aiohttp.ClientSession() as session:
            # --- ЭТАП 1 и 2 (без изменений) ---
            # ... (весь код загрузки и повторной обработки остается здесь)
            print("\n--- Этап 1: Основной проход по страницам ---")
            page_num = 0
            total_pages = None
            is_last_page = False
            while not is_last_page:
                if total_pages and page_num > total_pages:
                    page_num = total_pages - 1
                    if driver:
                        driver.quit()
                    driver, token = get_token()
                progress_info = f"/{total_pages}" if total_pages is not None else ""
                print(f"\n📄 Запрос данных со страницы {page_num + 1}{progress_info}")
                params = {**PARAMS, 'page': page_num}
                result = await fetch_async(session, URL_DOC_LIST, token, params=params)
                if result:
                    if total_pages is None:
                        total_pages = result.get("totalPages")
                        if total_pages is not None: print(f"✅ Обнаружено общее количество страниц: {total_pages}")
                    content = result.get("content", [])
                    print(f"Получено {len(content)} записей.")
                    total_records_received += len(content)
                    if content:
                        saved_count = await save_page_content_to_db(content)
                        total_saved_count += saved_count
                    is_last_page = result.get("last", False)
                    if is_last_page:
                        print("✅ Это была последняя страница.")
                        break
                    page_num += 1
                else:
                    failed_requests.append({'url': URL_DOC_LIST, 'params': params})
                    page_num += 1

            if failed_requests:
                driver, token = get_token()
                print("\n--- 🔁 Этап 2: Повторная обработка неудавшихся запросов ---")
                permanently_failed = []
                for request_details in failed_requests:
                    page = request_details['params'].get('page')
                    if total_pages and page > total_pages:
                        driver.quit()
                        driver, token = get_token()
                    progress_info = f"/{total_pages}" if total_pages is not None else ""
                    print(f"\nПовторный запрос для страницы {page}{progress_info}...")
                    result = await fetch_async(session, request_details['url'], token, params=request_details['params'])
                    if result:
                        content = result.get("content", [])
                        print(f"✅ Успешно! Получено {len(content)} записей со страницы {page}.")
                        total_records_received += len(content)
                        if content:
                            saved_count = await save_page_content_to_db(content)
                            total_saved_count += saved_count
                    else:
                        print(f"❌ Страница {page} снова не загрузилась.")
                        permanently_failed.append(request_details)
                failed_requests = permanently_failed
        
        # --- ЭТАП 3: Пост-обработка после успешной загрузки ---
        print("\n--- Этап 3: Пост-обработка данных ---")
        
        print("Сохранение данных в t_tax_cabinet_erpn_api_block...")
        result_block = await async_save_pg(SQL_INSERT_ADD_FROM_ERPN_API)
        print(f"Результат: {result_block}")

        print("Удаление дубликатов из t_tax_cabinet_erpn_api...")
        
        # --- ИСПРАВЛЕНИЕ ЗДЕСЬ: Убираем 'await' ---
        result_duplicates = remove_duplicates("t_tax_cabinet_erpn_api")
        
        print(f"Результат: {result_duplicates}")

    except Exception as e:
        print(f"💥 Произошла непредвиденная ошибка в main: {e}")
    finally:
        if driver:
            print("\nЗакрытие браузера...")
            driver.quit()
        
        print("Завершение асинхронных задач...")
        await asyncio.sleep(0)
            
        # --- ФИНАЛЬНЫЙ ОТЧЕТ ---
        print("\n--- 📊 Итоги ---")
        print(f"📥 Всего получено записей от API: {total_records_received}")
        print(f"💾 Успешно сохранено в базу данных: {total_saved_count}")
        discrepancy = total_records_received - total_saved_count
        if discrepancy > 0:
            print(f"⚠️ Расхождение: {discrepancy} записей не было сохранено (вероятно, из-за ошибок конвертации).")
        if failed_requests:
            print("\n❌ Не удалось обработать следующие страницы:")
            for req in failed_requests:
                print(f"  - URL: {req['url']}, Параметры: {req['params']}")
        else:
            print("\n🎉 Все запросы успешно обработаны!")
            
        print("\n✅ Скрипт завершил работу.")
        


if __name__ == "__main__":
    start_time = datetime.now()
    asyncio.run(main())
    end_time = datetime.now()
    print(f"⏱️ Общее время выполнения: {end_time - start_time}")