import asyncio
import os
import time
from datetime import datetime
from pathlib import Path
import aiohttp
import requests
import win32api
import win32con
import win32gui
import win32process
from TaxGovUaConfig import authorize, get_bearer_token, check_ping
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from ChangeKeyBoard import set_keyboard_layout
from AsyncPostgresql import async_save_pg
from TaxGovUaConfig import remove_duplicates
from tables.t_tax_cabinet_erpn_api import SQL_INSERT_ERPN
from tables.t_tax_cabinet_erpn_api_block import SQL_INSERT_ADD_FROM_ERPN_API
cur_dir = os.path.dirname(os.path.abspath(__file__))
url_const = "https://cabinet.tax.gov.ua/ws/api/nlnk/nlnkhd?fromImpdate="
driver, token = None, None
wrong_urls = []
set_keyboard_layout()  # Меняем раскладку клавиатуры на английскую


def create_chrome_driver():
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument("--disable-gpu")

    # Специальные настройки для работы с заблокированным экраном
    chrome_options.add_argument("--disable-software-rasterizer")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-features=TranslateUI")
    chrome_options.add_argument("--disable-notifications")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_driver_path = os.path.join(Path(cur_dir).parent.__str__(), "chromedriver-win64", "chromedriver.exe")

    # Создаем сервис для ChromeDriver
    service = Service(chrome_driver_path)

    # Создаем драйвер
    driver = webdriver.Chrome(service=service, options=chrome_options)

    # Получаем handle окна браузера
    hwnd = win32gui.FindWindow(None, driver.title)

    # Устанавливаем окно как foreground window даже при заблокированном экране
    if hwnd:
        # Получаем текущий thread
        current_thread = win32api.GetCurrentThreadId()

        # Получаем thread окна, которое сейчас в фокусе
        foreground_window = win32gui.GetForegroundWindow()
        foreground_thread = win32process.GetWindowThreadProcessId(foreground_window)[0]

        # Присоединяем input contexts
        win32process.AttachThreadInput(current_thread, foreground_thread, True)

        # Устанавливаем окно браузера как активное
        win32gui.SetForegroundWindow(hwnd)
        win32gui.ShowWindow(hwnd, win32con.SW_SHOW)

        # Отсоединяем input contexts
        win32process.AttachThreadInput(current_thread, foreground_thread, False)

    return driver


def get_token(driver=None):
    if not driver:
        driver = create_chrome_driver()

    try:
        url_login = "https://cabinet.tax.gov.ua/login"
        driver.get(url_login)

        # Ждем, когда форма авторизации будет доступна
        authorize(driver)
        # Здесь пользователь может взаимодействовать с формой входа
        # Даже при заблокированном экране окно будет доступно

        # Получаем токен после успешной авторизации
        token = driver.execute_script("return localStorage.getItem('token')")

        return driver, token

    except Exception as e:
        print(f"Ошибка при получении токена: {e}")
        if driver:
            driver.quit()
        return None, None


# Создаем url с датой начала и конца периода. Нужно для получения количества страниц
async def get_pages_urls(date_in, date_out):
    date_in = parse(date_in).date()
    date_out = parse(date_out).date()
    interval_days = (date_out - date_in).days
    return [
        (
            f"{url_const}{(date_in + relativedelta(days=i)).strftime('%Y-%m-%d')}%2000:00:00"
            f"&toImpdate={(date_in + relativedelta(days=i)).strftime('%Y-%m-%d')}%2023:59:59"
            "&sort=impdate"
        )
        for i in range(interval_days + 1)
    ]


# получение количества страниц в данном периоде
async def get_page_count_async(url, session, semaphore, retries=3):
    global url_const, token, wrong_urls, driver
    async with semaphore:
        await asyncio.sleep(5)
        for attempt in range(retries):
            print(f"{datetime.now()}; url: {url}")
            if not token:
                if driver:
                    driver.quit()
                    driver = None
                driver, token = get_token()
                print(f"Update token: {datetime.now()}; {token}")
            headers = {"Authorization": token, "Content-Type": "application/json"}
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        result = await response.json()
                        return [url, result.get("totalPages")]
            except aiohttp.ClientError as e:
                print(f"Ошибка при запросе {url}: {e}")
                pass
            except asyncio.TimeoutError:
                pass
            await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой
            _, token = get_token(driver=driver)

    wrong_urls.append(url)
    return [0, 0]


async def get_page_count(url):
    global url_const, token, wrong_urls
    headers = {"Authorization": token, "Content-Type": "application/json"}
    response = requests.get(url, headers=headers, timeout=20)
    if response and response.status_code == 200:
        result = response.json()
        return [url, result.get("totalPages")]
    wrong_urls.append(url)
    return [0, 0]


# Асинхронная функция для выполнения HTTP-запроса
async def fetch(url, retries=3):
    global driver, token, wrong_urls
    for attempt in range(retries):
        response = None
        if not token:
            if driver:
                driver.quit()
                driver = None
            driver, token = get_token()
            print(f"Update token: {datetime.now()}; {token}")
        headers = {"Authorization": token, "Content-Type": "application/json"}
        try:
            response = requests.get(url, headers=headers, timeout=20)
            if response and response.status_code == 200:
                return response.json()
        except Exception as e:
            pass

        time.sleep(2 ** attempt)
        if driver and (not response or response.status_code != 200):
            driver.quit()
            driver = None

    print(f"count wrong_urls: {len(wrong_urls)}; url: {url}")
    wrong_urls.append(url)
    return None


# отбираем url с количеством страниц, у которых количество страниц больше 0
async def create_url_with_pages(urls_pages):
    return [[url, pages] for url, pages in urls_pages if pages > 0]


# Создаем url для периода с номером страницы.
# Количество url = количество страниц
async def create_urls(url_pages):
    all_urls = []
    for url, page_number in url_pages:
        urls = [f"{url}&page={page}" for page in range(page_number)]
        all_urls.extend(urls)
    return all_urls


# Асинхронная функция для выполнения HTTP-запроса
async def fetch_async(url, session, semaphore, retries=3):
    global driver, token, wrong_urls
    async with semaphore:
        await asyncio.sleep(2)
        headers = {"Authorization": token, "Content-Type": "application/json"}
        for attempt in range(retries):
            try:
                async with session.get(url, headers=headers, timeout=20) as response:
                    if response and response.status == 200:
                        return await response.json()
            except Exception as e:
                await asyncio.sleep(2 ** attempt)  # Экспоненциальная задержка перед повторной попыткой
                _, token = get_token(driver=driver)

    wrong_urls.append(url)
    return None


# Асинхронная функция для запуска задач по 5 штук одновременно
async def run_tasks(urls, pages=False):
    semaphore = asyncio.Semaphore(5)  # Ограничение на 5 одновременных задач
    async with aiohttp.ClientSession() as session:
        if not pages:
            tasks = [fetch(url) for url in urls]
        else:
            tasks = [get_page_count_async(url, session, semaphore) for url in urls]
        results = await asyncio.gather(*tasks)
    return results


# получаем content из json
async def get_content_from_response(responses):
    contents = [r.get("content") for r in responses if r]
    return [data_dict.values() for data_list in contents for data_dict in data_list]


# Преобразование данных в нужный формат
async def convert_data(data):
    data[3] = int(data[3]) if data[3] else 0  # Преобразование nmr в int
    data[11] = int(data[11]) if data[11] else 0  # Преобразование corrnmr в int
    data[4] = parse(data[4]).date() if data[4] else data[4]
    data[13] = parse(data[13]).date() if data[13] else data[13]
    data[18] = parse(data[18]).date() if data[18] else data[18]
    return tuple(data)


async def main(date_from, date_to):
    global wrong_urls, driver, token
    urls = await get_pages_urls(date_from, date_to)
    urls_pages = await run_tasks(urls, pages=True)
    repeat_count = 1
    while len(wrong_urls) > 0:
        _, token = get_token(driver=driver)
        await asyncio.sleep(5)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL страниц: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        urls_pages.extend(await run_tasks(urls, pages=True))

    urls_pages_greater_0 = await create_url_with_pages(urls_pages)
    urls = await create_urls(urls_pages_greater_0)
    responses = await run_tasks(urls)
    repeat_count = 1
    while len(wrong_urls) > 0:
        _, token = get_token(driver=driver)
        await asyncio.sleep(5)
        print(f"{repeat_count} попытка. Кол-во незагруженных URL: {len(wrong_urls)}")
        repeat_count += 1
        urls = wrong_urls
        wrong_urls = []
        responses.extend(await run_tasks(urls))

    data = await get_content_from_response(responses)
    data = [await convert_data(list(d)) for d in data]
    data = remove_duplicates(data)
    await async_save_pg(data, SQL_INSERT_ERPN, SQL_INSERT_ADD_FROM_ERPN_API)


if __name__ == "__main__":
    print("Start", datetime.now())
    date_from = "01.01.2024"
    date_to = "31.01.2024"
    asyncio.run(main(date_from, date_to))
    if driver:
        driver.quit()
    print("End", datetime.now())
