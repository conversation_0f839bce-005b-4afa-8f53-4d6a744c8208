# -*- coding: utf-8 -*-
# DeleteDoubleRowsFromTable.py - Оптимизированная версия

import logging
import time
from typing import Optional
from AsyncPostgresql import con_postgres_psycopg2

# Настройка логирования
logger = logging.getLogger(__name__)

# Конфигурация
MAX_RETRIES = 3
RETRY_DELAY = 2


def delete_double_rows_from_table(table_name: str = 'medoc_reestr') -> bool:
    """
    Удаление дубликатов из таблицы с повторными попытками и улучшенной обработкой ошибок.

    Args:
        table_name: Имя таблицы для очистки от дубликатов

    Returns:
        bool: True если операция успешна, False в противном случае
    """

    for attempt in range(1, MAX_RETRIES + 1):
        conn = None
        try:
            # Параметры подключения к базе данных
            conn = con_postgres_psycopg2()

            # SQL-запрос для проверки существования таблицы
            query_check_table = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                );
            """

            with conn.cursor() as cursor:
                cursor.execute(query_check_table, (table_name,))
                table_exists = cursor.fetchone()[0]

                if not table_exists:
                    logger.warning(f"Table '{table_name}' does not exist")
                    return False

            # SQL-запрос для получения наименований всех полей таблицы
            query_columns = """
                SELECT column_name
                FROM information_schema.columns
                WHERE table_schema = 'public' 
                AND table_name = %s
                ORDER BY ordinal_position
            """

            # Выполнение запроса и получение результатов
            with conn.cursor() as cursor:
                cursor.execute(query_columns, (table_name,))
                columns = cursor.fetchall()

            if not columns:
                logger.warning(f"No columns found for table '{table_name}'")
                return False

            # Формирование списка имен столбцов
            column_names = [column[0] for column in columns]
            logger.info(f"Found {len(column_names)} columns in table '{table_name}'")

            # Подсчет дубликатов перед удалением
            query_count_duplicates = f"""
                SELECT COUNT(*) - COUNT(DISTINCT ({', '.join(column_names)}))
                FROM {table_name}
            """

            with conn.cursor() as cursor:
                cursor.execute(query_count_duplicates)
                duplicate_count = cursor.fetchone()[0]

                if duplicate_count == 0:
                    logger.info(f"No duplicates found in table '{table_name}'")
                    return True

                logger.info(f"Found {duplicate_count} duplicate rows in table '{table_name}'")

            # Формирование SQL-запроса для удаления дубликатов
            # Используем более безопасный подход с временной таблицей для больших объемов
            if duplicate_count > 1000:
                # Для больших таблиц используем временную таблицу
                logger.info(f"Using temporary table approach for {duplicate_count} duplicates")
                query_delete_duplicates = f"""
                    BEGIN;

                    -- Создаем временную таблицу с уникальными записями
                    CREATE TEMP TABLE temp_{table_name} AS
                    SELECT DISTINCT ON ({', '.join(column_names)}) *
                    FROM {table_name}
                    ORDER BY {', '.join(column_names)}, ctid;

                    -- Очищаем оригинальную таблицу
                    TRUNCATE {table_name};

                    -- Копируем данные обратно
                    INSERT INTO {table_name}
                    SELECT * FROM temp_{table_name};

                    -- Удаляем временную таблицу
                    DROP TABLE temp_{table_name};

                    COMMIT;
                """
            else:
                # Для небольших таблиц используем стандартный подход
                # Разбиваем на батчи для избежания таймаутов
                logger.info(f"Using standard DELETE approach for {duplicate_count} duplicates")
                query_delete_duplicates = f"""
                    DELETE FROM {table_name}
                    WHERE ctid IN (
                        SELECT ctid FROM (
                            SELECT ctid,
                                   ROW_NUMBER() OVER (PARTITION BY {', '.join(column_names)} ORDER BY ctid) AS rnum
                            FROM {table_name}
                        ) t
                        WHERE t.rnum > 1
                        LIMIT 100
                    )
                """

            # Выполнение запроса на удаление дубликатов
            with conn.cursor() as cursor:
                if duplicate_count > 1000:
                    # Для больших объемов выполняем транзакцию целиком
                    cursor.execute(query_delete_duplicates)
                    deleted_count = duplicate_count  # Примерная оценка
                else:
                    # Для небольших объемов удаляем батчами
                    deleted_count = 0
                    while True:
                        cursor.execute(query_delete_duplicates)
                        batch_deleted = cursor.rowcount
                        if batch_deleted == 0:
                            break
                        deleted_count += batch_deleted
                        conn.commit()
                        logger.info(f"Deleted batch of {batch_deleted} duplicates, total: {deleted_count}")

                        # Небольшая пауза между батчами
                        if batch_deleted == 100:
                            time.sleep(0.1)

                conn.commit()
                logger.info(f"Successfully deleted {deleted_count} duplicate rows from table '{table_name}'")

            return True

        except Exception as e:
            logger.error(f"Error deleting duplicates from table '{table_name}' (attempt {attempt}): {e}")

            # Откат транзакции при ошибке
            if conn:
                try:
                    conn.rollback()
                except:
                    pass

            if attempt < MAX_RETRIES:
                logger.info(f"Retrying in {RETRY_DELAY * attempt} seconds...")
                time.sleep(RETRY_DELAY * attempt)
            else:
                logger.critical(f"Failed to delete duplicates from table '{table_name}' after {MAX_RETRIES} attempts")
                return False

        finally:
            # Закрытие соединения
            if conn:
                try:
                    conn.close()
                except:
                    pass

    return False


def delete_double_rows_from_multiple_tables(table_names: list) -> dict:
    """
    Удаление дубликатов из нескольких таблиц.

    Args:
        table_names: Список имен таблиц

    Returns:
        dict: Словарь с результатами для каждой таблицы
    """
    results = {}

    for table_name in table_names:
        logger.info(f"Processing table: {table_name}")
        success = delete_double_rows_from_table(table_name)
        results[table_name] = success

        if not success:
            logger.warning(f"Failed to process table: {table_name}")

    # Вывод итогов
    successful = sum(1 for success in results.values() if success)
    logger.info(f"Processed {len(table_names)} tables: {successful} successful, {len(table_names) - successful} failed")

    return results


if __name__ == '__main__':
    # Настройка логирования для standalone запуска
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Обработка одной таблицы
    delete_double_rows_from_table('medoc_reestr')

    # Или обработка нескольких таблиц
    # tables = ['medoc_reestr', 'medoc_doc_info']
    # results = delete_double_rows_from_multiple_tables(tables)