# -*- coding: utf-8 -*-
"""
Скрипт для извлечения блокированных документов из базы данных, скачивания их и квитанций, и объединения в один PDF файл.
Логика объединения: вначале документ, потом квитанции в порядке номеров.
"""

# Обновлено: Добавлена обработка ошибок при скачивании PDF файлов, логирование неудачных попыток и объединение PDF.

from dotenv import load_dotenv
import psycopg2
import os
import pandas as pd
import requests
import subprocess
from TaxGovUaConfig import get_token
from PyPDF2 import PdfMerger

load_dotenv()

PG_USER = os.getenv("PG_USER")
PG_PASSWORD = os.getenv("PG_PASSWORD")
PG_HOST_LOCAL = os.getenv("PG_HOST_LOCAL")
PG_PORT = os.getenv("PG_PORT")
PG_DBNAME = os.getenv("PG_DBNAME")

error_logs = []

connection_params = {
    'host': PG_HOST_LOCAL,
    'database': PG_DBNAME,
    'user': PG_USER,
    'password': PG_PASSWORD,
    'port': int(PG_PORT) if PG_PORT else None
}


def get_block_documents():
    # Получаем список всех блокированных документов за год и сохраняем их в pandas DataFrame
    df = pd.DataFrame()
    conn = None
    try:
        sql = """
            SELECT 
                jsonb_strip_nulls(jsonb_build_object(
                    'kvt1','',
                    'kvt2', CASE WHEN kvt2 <> 0 THEN 2 ELSE NULL END,
                    'kvt3', CASE WHEN kvt3 <> 0 THEN 3 ELSE NULL END,
                    'kvt4', CASE WHEN kvt4 <> 0 THEN 4 ELSE NULL END
                )) AS kvts,
                "дата_РН_ВН",
                "номер_РН_ВН",
                "дата_клиента",
                "номер_клиента",
                "дата_РН_к_возврату",
                "номер_РН_к_возврату",
                t_erpn.*
            FROM public.t_tax_cabinet_erpn_api AS t_erpn
                INNER JOIN v_one_court_erpn AS v_erpn
                    ON t_erpn.code = v_erpn."код"
            WHERE crtdate::date >= '01.12.2024'::date and crtdate::date < '01.01.2025'::date
                AND (hsmcstt in (1, 2, 13, 14, 15, 18) OR hsmcsttname ILIKE ANY (ARRAY['%вiдмовлено%','%зупинено%']))
                -- AND ftype = 0
            ;
        """
        conn = psycopg2.connect(**connection_params)
        df = pd.read_sql_query(sql, conn)
        return df
    except Exception as e:
        print(f"Ошибка при получении данных из базы: {e}")
        return df
    finally:
        if conn:
            conn.close()


def created_folder(row: pd.Series):
    # Создаем папку для сохранения PDF файла
    doc_type = get_doc_type(row)
    buyer_code = row['cptin']  # cptin - код покупателя
    doc_date = row['дата_РН_ВН']
    period_folder = doc_date.strftime('%Y.%m')
    folder = os.path.join(os.path.dirname(__file__), 'ERPN', str(buyer_code), doc_type, period_folder)
    os.makedirs(folder, exist_ok=True)
    return folder


def get_doc_type(row: pd.Series):
    if row['ftype'] == 0:  # 0-НН;1-РК
        doc_type = "ПН"
    else:
        doc_type = "РК"
    return doc_type
    

def get_erpn_filename_and_urls(row: pd.Series):
    # Скачиваем PDF файл
    urls = []
    url = f"https://cabinet.tax.gov.ua/ws/api/file/nlnkhd/pdf?"  # code={code}&impdate={impdate}"
    doc_type = get_doc_type(row)
    file_name = get_file_name(row, doc_type)
    urls.append({'file_name': file_name,'url': url})
    kvts = row['kvts']
    for i, kvt in enumerate(kvts.values()):
        doc_type = f"KVT{i+1}"
        file_name = get_file_name(row, doc_type)
        url = f"https://cabinet.tax.gov.ua/ws/api/file/nlnkhd/pdf/kvt{kvt}?"  # code={code}&impdate={impdate}"
        urls.append({'file_name': file_name,'url': url})
    return urls


def get_file_name(row: pd.Series, kvt: str):
    # Создаем имя файла
    folder = created_folder(row)
    doc_type = get_doc_type(row)
    if not 'KVT' in kvt:
        kvt = ''
        
    doc_date = row['дата_РН_ВН'].strftime('%Y %m %d')
    doc_number = int(row['nmr'])
    file_name = os.path.join(folder, f"{doc_type} {doc_number} {doc_date} {kvt}".strip() + ".pdf")
    return file_name


def download_pdf(doc: dict, params: dict, token: str):
    # Скачиваем PDF файл
    headers = {
        'Authorization': token,
        'Content-Type': 'application/pdf',
    }
    url = doc['url']
    filename = doc['file_name']
    response = requests.get(url, headers=headers, params=params)
    if response.status_code == 200:
        with open(filename, 'wb') as f:
            f.write(response.content)
        print(f"Файл {filename} успешно сохранен.")
    else:
        print(f"Ошибка при скачивании файла {url}. Статус: {response.status_code}")
        return None
    return filename


def merge_pdfs(file_list: list, row: pd.Series, folder: str):
    # Объединяем PDF файлы в один
    if not file_list:
        return

    merger = PdfMerger()

    # Добавляем файлы в порядке: основной документ, затем квитанции
    for pdf_file in file_list:
        try:
            merger.append(pdf_file)
        except Exception as e:
            print(f"Ошибка при добавлении файла {pdf_file}: {e}")

    # Создаем имя для объединенного файла на основе основного документа
    main_file = file_list[0]
    base_name = os.path.basename(main_file).replace('.pdf', '')
    merged_filename = f"{base_name} merged.pdf"
    merged_path = os.path.join(folder, merged_filename)

    # Сохраняем объединенный файл
    try:
        with open(merged_path, 'wb') as output_file:
            merger.write(output_file)
        print(f"Объединенный файл создан: {merged_filename}")
    except Exception as e:
        print(f"Ошибка при сохранении объединенного файла: {e}")
    finally:
        merger.close()


def cycle_df(df: pd.DataFrame):
    # Цикл по датафрейму и сохранение PDF файлов
    driver, token = get_token()

    for count, (idx, row) in enumerate(df.iterrows()):

        # Создаем папку для сохранения PDF файла в формате: ERPN\Код_покупателя\doc_type\period
        folder = created_folder(row)
        print(f"Сохраняем файл: {folder}")
        impdate = row['impdate'].strftime('%Y-%m-%d %H:%M:%S')
        code = row['code']
        params = {
            'code': code,
            'impdate': impdate
        }

        # Получаем список файлов, квитанций и их URL для скачивания. НН/РК и квитанции 1, 2, 3, 4
        docs = get_erpn_filename_and_urls(row)
        downloaded_files = []
        for doc in docs:
            print(f"Скачиваем файл: {doc['file_name']}")
            filename = download_pdf(doc, params, token)
            if filename:
                downloaded_files.append(filename)

        # Объединяем все скачанные PDF в один файл
        if downloaded_files:
            merge_pdfs(downloaded_files, row, folder)

        if count % 50 == 0 and driver:
            # обновляем страницу
            driver.refresh()

    if driver:
        driver.quit()


def  pdf_all_for_block_documents_main():
    # главный метод. Извлекаем все блокировки за год

    # Запускаем предварительный скрипт
    try:
        print("Запускаем предварительный скрипт CreateAndRunSQLScripts.py...")
        result = subprocess.run([
            "C:/Rasim/Python/Prestige/.venv/Scripts/python.exe",
            "c:/Rasim/Python/Prestige/CreateAndRunSQLScripts.py"
        ], capture_output=True, text=True, encoding='utf-8')
        print("Вывод скрипта:")
        print(result.stdout)
        if result.stderr:
            print("Ошибки скрипта:")
            print(result.stderr)
        print(f"Код завершения: {result.returncode}")
    except Exception as e:
        print(f"Ошибка при запуске предварительного скрипта: {e}")

    df = get_block_documents()
    if df.empty:
        print("Нет блокированных документов за последний год.")
        return
    count = df.count().iloc[0]
    print(f"Найдено {count} блокированных документов за последний год.")
    cycle_df(df)
    
    
if __name__ == "__main__":
    pdf_all_for_block_documents_main()
